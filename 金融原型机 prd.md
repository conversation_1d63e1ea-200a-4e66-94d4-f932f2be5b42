# 银信通AI Agent 产品需求文档 (PRD)

## 一、产品概述

### 1.1 产品背景

银信通是银行短信提醒服务，用户可以在账户发生资金变动时收到短信通知。目前该服务存在以下核心痛点：

- **功能入口深**：在柜员机上非常难找，用户需要在复杂的菜单中寻找
- **修改功能不灵活**：无法自定义最低通知金额等个性化设置
- **解约流程繁琐**：比签约更麻烦，有时甚至要求去柜台
- **隐性收费问题**：月费2-3元，用户可能忘记已签约而持续付费

### 1.2 产品目标

构建一个AI驱动的智能银行服务助手，通过自然语言交互和智能引导，将传统的银信通办理流程从15-20分钟缩短到2-3分钟，同时提供个性化服务和主动关怀。

### 1.3 目标用户

- 需要办理银信通服务的银行客户
- 对传统银行服务流程感到困惑的用户
- 希望优化银行服务费用的用户
- 追求高效、便捷服务体验的用户

## 二、现有业务流程分析

### 2.1 银信通签约流程（传统）

**柜台流程**：

1. 携带身份证和银行卡
1. 告知柜员需要开通短信提醒
1. 提供手机号码
1. 输入密码确认

**传统柜员机流程**：

1. 携带身份证+银行卡
1. 在主页服务菜单寻找”银信通签约”等功能（难找）
1. 填写/签署《银信通服务协议》
1. 选择需要签约的银行卡账户
1. 确认需要绑定的手机号码
1. 接收验证码和起点通知金额确认
1. 柜员介入审核授权
1. 信息采集与授权，用户确认无误后签名
1. 柜员指纹授权后办理成功

### 2.2 银信通修改流程（传统）

1. 在等待插卡页面选择【业务签约】→【银信通】→【银信通修改】
1. 插入银行卡，身份证读取验证
1. 显示修改信息录入页面
1. 选择需要修改的账户
1. 确认更换的手机号码
1. 接收验证码确认
1. 柜员审核授权
1. 签名确认
1. 柜员指纹授权完成

### 2.3 银信通解约流程（传统）

1. 在复杂菜单中找到解约入口
1. 身份验证（身份证+银行卡）
1. 查看当前签约信息
1. 选择授权柜员
1. 人脸识别授权
1. 阅读并签署解约授权书
1. 信息核对和签名
1. 柜员指纹授权
1. 解约成功

## 三、AI优化后的服务流程

### 3.1 第一阶段：智能感知与主动服务

**功能需求**：

1. **主动识别与问候**
- AI检测到用户插卡/登录后主动发起对话
- 基于用户历史数据智能判断潜在需求
- 示例文案：“您好！我是您的AI银行助手小慧。检测到您使用的是新办理的工资卡，是否需要为这张卡开通账户变动提醒服务？”
1. **情境感知**
- 识别用户账户类型（工资卡/储蓄卡/信用卡）
- 分析账户使用频率和交易模式
- 预判用户可能的服务需求
1. **界面设计要求**
- 友好的AI形象展示（渐变色圆形图标）
- 打字机效果的文字展示
- 快捷操作按钮组（开通服务/修改设置/取消服务）

### 3.2 第二阶段：需求探索与方案共创

**功能需求**：

1. **对话式需求挖掘**
- 通过自然语言理解用户意图
- 支持模糊需求的clarification
- 示例对话：
  - 用户：“我想收到工资通知”
  - AI：“明白了，您希望在工资到账时收到通知。建议同时设置支出提醒，这样可以全面掌握账户动态。您看如何？”
1. **AI思维链可视化**
- 实时展示AI的分析过程
- 包含以下思考步骤：
  
  ```
  ▸ 检测到您使用的是工资卡
  ▸ 分析您的账户使用模式
  ▸ 您的月均交易次数：15-20笔
  ▸ 生成个性化通知方案
  ```
1. **智能方案生成与展示**
- 生成2-3个推荐方案
- 每个方案包含：
  - 方案名称
  - 具体配置（通知金额阈值、通知类型）
  - 月费
  - 推荐理由
  - 置信度百分比
   
   **方案示例**：
   
   ```
   推荐方案（置信度95%）
   • 收入：全额通知
   • 支出：≥50元通知  
   • 月费：2元
   • 推荐理由：工资卡建议关注所有收入，过滤日常小额消费
   ```
1. **动态方案调整**
- 支持用户实时修改方案参数
- 即时预览修改效果
- 记录用户偏好用于优化
1. **待办事项清单生成**
   
   ```
   ✓ 银行卡信息（已自动读取）
   ⏳ 手机号码确认
   ⏳ 身份验证
   ⏳ 服务协议确认
   ```

### 3.3 第三阶段：确认与授权执行

**功能需求**：

1. **大白话方案复述**
   
   ```
   📋 即将为您办理：
   • 为尾号8899的工资卡开通短信提醒
   • 收入：全部通知
   • 支出：50元及以上通知  
   • 每月费用：2元
   • 首次扣费：下月1日
   ```
1. **动态风险授权**
- 低风险操作：密码验证
- 中风险操作：密码+短信验证
- 高风险操作：密码+人脸识别
1. **实时执行可视化**
- 步骤化展示执行进度
- 每个步骤包含：
  - 步骤序号
  - 步骤描述
  - 当前状态（待处理/处理中/已完成）
  - 预计时间
   
   **执行步骤示例**：
   
   ```
   1. ⏳ 验证身份信息
   2. ⏳ 读取银行卡信息  
   3. ⏳ 确认手机号码
   4. ⏳ 生成服务协议
   5. ⏳ 提交银行系统
   6. ⏳ 等待系统确认
   ```
1. **异常处理机制**
- 错误时的友好提示
- 提供错误代码和解决方案
- 一键回滚功能

### 3.4 第四阶段：成功反馈与延伸服务

**功能需求**：

1. **成功动画与凭证**
- 成功动画效果（绿色勾选动画）
- 生成唯一业务ID
- 提供电子凭证下载
1. **智能延伸服务推荐**
- 基于刚完成的业务推荐相关服务
- 示例：
  - 签约后→”需要设置工资到账提醒吗？”
  - 解约后→”这张卡已长期未用，了解一下睡眠户政策？”
1. **轻量化反馈机制**
- 快速标签式评价
- 选项：流程顺畅/AI很智能/需要改进
- 不满意时提供原因选项

## 四、特殊场景处理

### 4.1 批量优化场景

当AI检测到用户有多个可优化项时，主动提供批量优化建议：

```
📊 银信通服务优化建议：

1. 重复签约
   • 两张借记卡都开通了服务，但其中一张已停用
   • 建议：解约停用卡，每年节省24元

2. 设置优化  
   • 信用卡设置全额通知，每天收到10+条短信
   • 建议：调整为500元以上通知

3. 费用优化
   • 已累计支付156元
   • 建议：开通年付享9折

[一键优化] 预计每年节省32.4元
```

### 4.2 智能解约场景

```
📊 您的银信通服务分析：
• 共有2张卡开通服务
• 尾号6789储蓄卡：近3个月无交易，持续扣费6元
• 尾号1234信用卡：活跃使用中

💡 AI建议：建议解约尾号6789的服务
```

## 五、UI/UX设计规范

### 5.1 视觉风格

- **主题**：深色主题为主，提供浅色切换选项
- **色彩系统**：
  - 主色：蓝色(#3B82F6) + 紫色(#8B5CF6)渐变
  - 背景：深灰色层次(#030712, #111827, #1F2937)
  - 成功：绿色(#10B981)
  - 警告：黄色(#F59E0B)
  - 错误：红色(#EF4444)

### 5.2 组件设计

1. **AI对话气泡**
- 左对齐，带AI头像
- 支持富文本内容
- 动画进入效果
1. **方案卡片**
- 悬浮高亮效果
- 推荐标签醒目展示
- 点击反馈动画
1. **执行进度条**
- 步骤化展示
- 实时状态更新
- 完成动画效果
1. **输入组件**
- 智能提示
- 实时验证
- 错误提示

### 5.3 交互动效

- 所有状态切换使用渐变动画(300ms)
- 加载状态使用脉冲动画
- 成功/失败使用弹性动画
- 数字变化使用计数动画

## 六、技术实现建议

### 6.1 前端技术栈

```javascript
// 推荐技术栈
{
  "框架": "原生HTML/CSS/JS (便于快速原型)",
  "样式": "Tailwind CSS",
  "动画": "CSS Animation + JavaScript",
  "图标": "SVG内联图标",
  "构建": "Vite (可选)"
}
```

### 6.2 核心功能模块

1. **AIAgent类**：处理AI逻辑和状态管理
1. **DialogManager类**：管理对话流程
1. **AnimationController类**：控制动画效果
1. **FormValidator类**：智能表单验证
1. **DataVisualizer类**：数据可视化

### 6.3 数据模拟

```javascript
// 模拟用户数据
const mockUserData = {
  cards: [
    {
      number: "8899",
      type: "工资卡",
      hasService: false,
      monthlyTransactions: 18
    },
    {
      number: "6789", 
      type: "储蓄卡",
      hasService: true,
      lastTransaction: "3个月前",
      monthlyFee: 2
    }
  ],
  preferences: {
    notificationThreshold: 50,
    preferredChannel: "SMS"
  }
}
```

## 七、成功指标

1. **效率指标**
- 办理时间从15分钟缩短到3分钟
- 操作步骤从9步简化到4步
1. **体验指标**
- 用户满意度提升40%
- 一次性办理成功率95%
1. **业务指标**
- 减少80%人工介入
- 主动发现并解决隐性收费问题

## 八、开发指导

### 8.1 第一阶段：基础框架（2小时）

1. 搭建HTML结构
1. 实现深色主题样式
1. 创建AI对话界面

### 8.2 第二阶段：核心功能（4小时）

1. 实现三个主要场景流程
1. 添加AI思维可视化
1. 实现方案生成和展示

### 8.3 第三阶段：交互优化（2小时）

1. 添加动画效果
1. 实现实时验证
1. 优化响应式设计

### 8.4 第四阶段：完善细节（2小时）

1. 异常处理
1. 性能优化
1. 演示数据准备

## 九、注意事项

1. **保持简洁**：Demo重在展示核心价值，避免过度设计
1. **突出AI特性**：强调思维透明、主动服务、个性化
1. **注重细节**：动画流畅、反馈及时、文案友好
1. **易于演示**：预设多个场景路径，方便展示不同功能

-----

**使用此PRD的说明**：

1. 将此文档提供给Cursor AI，说明要基于此PRD开发一个网页Demo
1. 强调这是一个AI Agent产品，需要体现AI的智能特性
1. 参考Perplexity、Claude等AI产品的设计风格
1. 重点实现文档中描述的四个阶段流程



# Cursor AI 银信通AI Agent 开发指南

## 项目概述

创建一个现代化的金融AI Agent产品demo，展示银信通（银行短信提醒服务）的智能化办理流程。产品需要体现AI Agent的核心特征：主动性、智能性、透明性和人性化。

## 设计理念

### 参考产品

- **Perplexity**: 清晰的思维过程展示，极简的界面设计
- **Claude**: 友好的对话体验，清晰的信息层级
- **ChatGPT**: 流畅的交互动画，智能的上下文理解
- **V0**: 实时的代码/方案生成展示

### 核心设计原则

1. **AI思维可视化**: 展示AI的推理过程，增强信任感
1. **渐进式披露**: 信息分层展示，避免认知过载
1. **微交互动画**: 每个操作都有即时反馈
1. **深色主题优先**: 现代AI产品的视觉语言
1. **卡片式布局**: 信息模块化，易于理解

## 技术栈建议

```markdown
- HTML5 + CSS3 (使用CSS Variables实现主题切换)
- Vanilla JavaScript (保持轻量，易于演示)
- Tailwind CSS (快速构建现代UI)
- Framer Motion 或 GSAP (流畅动画效果)
- Prism.js (展示AI生成的代码/配置)
```

## 项目结构

```
banking-ai-agent/
├── index.html          # 主页面
├── styles/
│   ├── main.css       # 主样式文件
│   ├── animations.css # 动画效果
│   └── themes.css     # 主题变量
├── scripts/
│   ├── app.js         # 主逻辑
│   ├── ai-agent.js    # AI Agent核心逻辑
│   └── animations.js  # 动画控制
└── assets/
    ├── icons/         # 图标资源
    └── sounds/        # 音效文件
```

## 详细实现指南

### 1. 基础HTML结构

```html
<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银信通 AI Agent - 智能银行服务助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body class="bg-gray-950 text-gray-100">
    <!-- AI Agent 界面容器 -->
    <div id="ai-agent-container" class="min-h-screen flex flex-col">
        
        <!-- 顶部导航栏 -->
        <nav class="border-b border-gray-800 bg-gray-900/50 backdrop-blur-xl">
            <div class="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold">AI</span>
                    </div>
                    <h1 class="text-xl font-semibold">银信通智能助手</h1>
                </div>
                <button class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>联系专家</span>
                </button>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="flex-1 max-w-7xl mx-auto w-full px-4 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- 左侧：对话区域 -->
                <div class="lg:col-span-2 space-y-4">
                    <!-- AI 思维过程展示区 -->
                    <div id="ai-thinking" class="bg-gray-900/50 rounded-2xl p-6 border border-gray-800 hidden">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-75"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                            </div>
                            <span class="text-sm text-gray-400">AI 正在思考...</span>
                        </div>
                        <div id="thinking-content" class="space-y-2 text-sm text-gray-300">
                            <!-- 动态插入思维链内容 -->
                        </div>
                    </div>

                    <!-- 对话消息区域 -->
                    <div id="chat-messages" class="space-y-4">
                        <!-- 欢迎消息 -->
                        <div class="ai-message">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <span class="text-white text-sm font-bold">AI</span>
                                </div>
                                <div class="flex-1 bg-gray-900 rounded-2xl p-4">
                                    <p class="text-gray-100">您好！我是您的AI银行助手。我可以帮您智能办理银信通服务，包括开通、修改和取消短信提醒。</p>
                                    <div class="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-3">
                                        <button onclick="startService('subscribe')" class="quick-action-btn">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            <span>开通服务</span>
                                        </button>
                                        <button onclick="startService('modify')" class="quick-action-btn">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            <span>修改设置</span>
                                        </button>
                                        <button onclick="startService('cancel')" class="quick-action-btn">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            <span>取消服务</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 动态方案展示区 -->
                    <div id="solution-cards" class="hidden space-y-4">
                        <!-- 动态插入方案卡片 -->
                    </div>

                    <!-- 执行进度展示 -->
                    <div id="execution-progress" class="hidden bg-gray-900/50 rounded-2xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold mb-4">执行进度</h3>
                        <div class="space-y-3">
                            <!-- 动态插入执行步骤 -->
                        </div>
                    </div>
                </div>

                <!-- 右侧：信息面板 -->
                <div class="space-y-4">
                    <!-- 账户信息卡片 -->
                    <div class="bg-gray-900/50 rounded-2xl p-6 border border-gray-800">
                        <h3 class="text-lg font-semibold mb-4">账户信息</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">当前卡号</span>
                                <span class="font-mono">**** 8899</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">卡片类型</span>
                                <span>工资卡</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">服务状态</span>
                                <span class="text-green-500">未开通</span>
                            </div>
                        </div>
                    </div>

                    <!-- AI 分析洞察 -->
                    <div id="ai-insights" class="bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl p-6 border border-blue-800/50">
                        <div class="flex items-center space-x-2 mb-3">
                            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold">AI 洞察</h3>
                        </div>
                        <p class="text-sm text-gray-300">基于您的账户类型，建议开通收入全额通知，支出50元以上通知，这样既不会错过重要交易，也不会被小额消费打扰。</p>
                    </div>

                    <!-- 安全提示 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800">
                        <div class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <div>
                                <h4 class="font-semibold text-sm">安全保障</h4>
                                <p class="text-xs text-gray-400 mt-1">全程加密传输，支持随时撤销</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部输入区域 -->
        <div class="border-t border-gray-800 bg-gray-900/50 backdrop-blur-xl">
            <div class="max-w-7xl mx-auto px-4 py-4">
                <div class="flex items-center space-x-4">
                    <input type="text" id="user-input" placeholder="告诉我您的需求..." 
                           class="flex-1 bg-gray-800 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="sendMessage()" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-xl font-medium transition-colors">
                        发送
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/app.js"></script>
</body>
</html>
```

### 2. 核心CSS样式 (main.css)

```css
/* CSS变量定义 */
:root {
    --bg-primary: #030712;
    --bg-secondary: #111827;
    --bg-tertiary: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #9ca3af;
    --accent-blue: #3b82f6;
    --accent-purple: #8b5cf6;
    --border-color: #374151;
}

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    antialiased: true;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

/* 快速操作按钮 */
.quick-action-btn {
    @apply flex items-center justify-center space-x-2 px-4 py-3 bg-gray-800 hover:bg-gray-700 rounded-xl transition-all hover:scale-105;
}

/* AI消息样式 */
.ai-message {
    animation: slideInLeft 0.3s ease-out;
}

/* 用户消息样式 */
.user-message {
    animation: slideInRight 0.3s ease-out;
}

/* 思维链展示动画 */
.thinking-step {
    animation: fadeIn 0.5s ease-out;
    @apply flex items-start space-x-2 py-2;
}

/* 方案卡片 */
.solution-card {
    @apply bg-gray-900 rounded-xl p-6 border-2 border-gray-800 hover:border-blue-600 transition-all cursor-pointer relative overflow-hidden;
}

.solution-card.recommended::before {
    content: '推荐';
    @apply absolute top-0 right-0 bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs px-3 py-1 rounded-bl-xl;
}

/* 执行步骤 */
.execution-step {
    @apply flex items-center space-x-3 p-3 rounded-lg transition-all;
}

.execution-step.pending {
    @apply bg-gray-800/50 text-gray-400;
}

.execution-step.active {
    @apply bg-blue-900/30 text-blue-400 border border-blue-800;
}

.execution-step.completed {
    @apply bg-green-900/30 text-green-400;
}

/* 动画定义 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    white-space: nowrap;
    animation: typing 2s steps(40, end);
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}
```

### 3. 核心JavaScript逻辑 (app.js)

```javascript
// AI Agent 核心类
class BankingAIAgent {
    constructor() {
        this.currentState = 'idle';
        this.currentService = null;
        this.userData = {
            cards: [
                { number: '8899', type: '工资卡', hasService: false },
                { number: '6789', type: '储蓄卡', hasService: true, lastTransaction: '3个月前' },
                { number: '1234', type: '信用卡', hasService: true }
            ]
        };
        this.thinkingSteps = [];
    }

    // 开始服务
    async startService(serviceType) {
        this.currentService = serviceType;
        this.showThinking();
        
        switch(serviceType) {
            case 'subscribe':
                await this.handleSubscription();
                break;
            case 'modify':
                await this.handleModification();
                break;
            case 'cancel':
                await this.handleCancellation();
                break;
        }
    }

    // 显示AI思考过程
    showThinking() {
        const thinkingDiv = document.getElementById('ai-thinking');
        const thinkingContent = document.getElementById('thinking-content');
        
        thinkingDiv.classList.remove('hidden');
        thinkingContent.innerHTML = '';
        
        const steps = this.getThinkingSteps();
        
        steps.forEach((step, index) => {
            setTimeout(() => {
                const stepElement = document.createElement('div');
                stepElement.className = 'thinking-step';
                stepElement.innerHTML = `
                    <span class="text-blue-400">▸</span>
                    <span>${step}</span>
                `;
                thinkingContent.appendChild(stepElement);
            }, index * 500);
        });

        setTimeout(() => {
            thinkingDiv.classList.add('hidden');
        }, steps.length * 500 + 1000);
    }

    // 获取思考步骤
    getThinkingSteps() {
        const stepsMap = {
            'subscribe': [
                '检测到您使用的是工资卡',
                '分析您的账户使用模式',
                '生成个性化通知方案',
                '准备签约所需材料'
            ],
            'modify': [
                '查询您的现有服务配置',
                '分析最近的通知频率',
                '识别可优化的设置项',
                '生成修改建议'
            ],
            'cancel': [
                '扫描所有已签约服务',
                '分析账户活跃度',
                '计算已产生费用',
                '评估解约影响'
            ]
        };
        
        return stepsMap[this.currentService] || [];
    }

    // 处理订阅服务
    async handleSubscription() {
        await this.delay(3000);
        
        // 显示方案选择
        this.showSolutionCards([
            {
                title: '智能推荐方案',
                description: '收入全额通知，支出≥50元通知',
                price: '2元/月',
                recommended: true,
                confidence: 95
            },
            {
                title: '全额通知方案',
                description: '所有交易都通知',
                price: '2元/月',
                confidence: 70
            },
            {
                title: '自定义方案',
                description: '按您的需求定制',
                price: '2元/月',
                confidence: 0
            }
        ]);
    }

    // 显示方案卡片
    showSolutionCards(solutions) {
        const container = document.getElementById('solution-cards');
        container.innerHTML = '';
        container.classList.remove('hidden');
        
        solutions.forEach((solution, index) => {
            const card = document.createElement('div');
            card.className = `solution-card ${solution.recommended ? 'recommended' : ''}`;
            card.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <h4 class="text-lg font-semibold">${solution.title}</h4>
                    ${solution.confidence > 0 ? `
                        <span class="text-sm text-gray-400">置信度 ${solution.confidence}%</span>
                    ` : ''}
                </div>
                <p class="text-gray-300 mb-4">${solution.description}</p>
                <div class="flex justify-between items-center">
                    <span class="text-xl font-bold text-blue-400">${solution.price}</span>
                    <button onclick="selectSolution(${index})" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                        选择此方案
                    </button>
                </div>
            `;
            
            container.appendChild(card);
        });
    }

    // 执行流程
    async executeProcess(steps) {
        const progressDiv = document.getElementById('execution-progress');
        progressDiv.classList.remove('hidden');
        
        const stepsContainer = progressDiv.querySelector('.space-y-3') || 
                              (() => {
                                  const div = document.createElement('div');
                                  div.className = 'space-y-3';
                                  progressDiv.appendChild(div);
                                  return div;
                              })();
        
        stepsContainer.innerHTML = '';
        
        for (let i = 0; i < steps.length; i++) {
            const stepElement = document.createElement('div');
            stepElement.className = 'execution-step pending';
            stepElement.innerHTML = `
                <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
                    <span class="text-sm">${i + 1}</span>
                </div>
                <span>${steps[i]}</span>
            `;
            stepsContainer.appendChild(stepElement);
        }
        
        // 逐步执行
        for (let i = 0; i < steps.length; i++) {
            await this.delay(1500);
            const stepElements = stepsContainer.querySelectorAll('.execution-step');
            
            if (i > 0) {
                stepElements[i - 1].classList.remove('active');
                stepElements[i - 1].classList.add('completed');
                stepElements[i - 1].querySelector('div').innerHTML = '✓';
            }
            
            stepElements[i].classList.remove('pending');
            stepElements[i].classList.add('active');
        }
        
        // 完成最后一步
        await this.delay(1500);
        const lastStep = stepsContainer.querySelectorAll('.execution-step')[steps.length - 1];
        lastStep.classList.remove('active');
        lastStep.classList.add('completed');
        lastStep.querySelector('div').innerHTML = '✓';
        
        // 显示成功消息
        this.showSuccessMessage();
    }

    // 显示成功消息
    showSuccessMessage() {
        const messagesDiv = document.getElementById('chat-messages');
        const successMessage = document.createElement('div');
        successMessage.className = 'ai-message';
        successMessage.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span class="text-white text-sm">✓</span>
                </div>
                <div class="flex-1 bg-gray-900 rounded-2xl p-4">
                    <p class="text-gray-100 font-semibold mb-2">🎉 办理成功！</p>
                    <p class="text-gray-300">您的银信通服务已成功开通。</p>
                    <div class="mt-4 bg-gray-800 rounded-lg p-3">
                        <p class="text-sm text-gray-400">业务凭证号：BXT${Date.now()}</p>
                        <p class="text-sm text-gray-400 mt-1">首次扣费时间：下月1日</p>
                    </div>
                    <div class="mt-4">
                        <button class="text-blue-400 hover:text-blue-300 text-sm">下载凭证</button>
                        <span class="text-gray-600 mx-2">|</span>
                        <button class="text-blue-400 hover:text-blue-300 text-sm">查看详情</button>
                    </div>
                </div>
            </div>
        `;
        messagesDiv.appendChild(successMessage);
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化AI Agent
const aiAgent = new BankingAIAgent();

// 全局函数
function startService(type) {
    aiAgent.startService(type);
}

function selectSolution(index) {
    // 隐藏方案选择
    document.getElementById('solution-cards').classList.add('hidden');
    
    // 执行办理流程
    const steps = [
        '验证身份信息',
        '读取银行卡信息',
        '确认手机号码',
        '生成服务协议',
        '提交银行系统',
        '等待系统确认'
    ];
    
    aiAgent.executeProcess(steps);
}

function sendMessage() {
    const input = document.getElementById('user-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // 添加用户消息
    const messagesDiv = document.getElementById('chat-messages');
    const userMessageDiv = document.createElement('div');
    userMessageDiv.className = 'user-message';
    userMessageDiv.innerHTML = `
        <div class="flex items-start space-x-3 justify-end">
            <div class="flex-1 bg-blue-900 rounded-2xl p-4 max-w-md">
                <p class="text-gray-100">${message}</p>
            </div>
            <div class="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center flex-shrink-0">
                <span class="text-white text-sm">您</span>
            </div>
        </div>
    `;
    messagesDiv.appendChild(userMessageDiv);
    
    // 清空输入
    input.value = '';
    
    // AI响应逻辑
    setTimeout(() => {
        // 根据用户输入判断意图
        if (message.includes('开通') || message.includes('签约')) {
            aiAgent.startService('subscribe');
        } else if (message.includes('修改') || message.includes('更改')) {
            aiAgent.startService('modify');
        } else if (message.includes('取消') || message.includes('解约')) {
            aiAgent.startService('cancel');
        } else {
            // 默认回复
            const aiResponse = document.createElement('div');
            aiResponse.className = 'ai-message';
            aiResponse.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span class="text-white text-sm font-bold">AI</span>
                    </div>
                    <div class="flex-1 bg-gray-900 rounded-2xl p-4">
                        <p class="text-gray-100">我理解您的需求。请问您是想要开通、修改还是取消银信通服务呢？</p>
                    </div>
                </div>
            `;
            messagesDiv.appendChild(aiResponse);
        }
    }, 1000);
}

// 监听回车键
document.getElementById('user-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});
```

### 4. 高级交互功能实现

```javascript
// 添加到 app.js

// 语音输入功能
class VoiceAssistant {
    constructor() {
        this.recognition = null;
        this.isListening = false;
        this.initializeVoiceRecognition();
    }

    initializeVoiceRecognition() {
        if ('webkitSpeechRecognition' in window) {
            this.recognition = new webkitSpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = true;
            this.recognition.lang = 'zh-CN';

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                document.getElementById('user-input').value = transcript;
            };

            this.recognition.onerror = (event) => {
                console.error('语音识别错误:', event.error);
            };
        }
    }

    toggleListening() {
        if (this.isListening) {
            this.recognition.stop();
            this.isListening = false;
        } else {
            this.recognition.start();
            this.isListening = true;
        }
    }
}

// 实时数据可视化
class DataVisualizer {
    constructor() {
        this.charts = {};
    }

    createComparisonChart(data) {
        const chartHTML = `
            <div class="bg-gray-900 rounded-xl p-6 mt-4">
                <h4 class="text-lg font-semibold mb-4">费用对比分析</h4>
                <div class="space-y-3">
                    ${data.map(item => `
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">${item.label}</span>
                            <div class="flex items-center space-x-3">
                                <div class="w-32 bg-gray-800 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" 
                                         style="width: ${item.percentage}%"></div>
                                </div>
                                <span class="text-sm font-mono">${item.value}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        return chartHTML;
    }

    animateNumber(element, start, end, duration) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.round(current);
        }, 16);
    }
}

// 智能表单验证
class SmartFormValidator {
    constructor() {
        this.rules = {
            phone: /^1[3-9]\d{9}$/,
            amount: /^\d+(\.\d{1,2})?$/,
            cardNumber: /^\d{4}$/
        };
    }

    validateField(fieldName, value) {
        const rule = this.rules[fieldName];
        if (!rule) return { valid: true };

        const valid = rule.test(value);
        const messages = {
            phone: '请输入有效的手机号码',
            amount: '请输入有效的金额',
            cardNumber: '请输入卡号后四位'
        };

        return {
            valid,
            message: valid ? '' : messages[fieldName]
        };
    }

    createSmartInput(fieldName, placeholder) {
        const inputHTML = `
            <div class="smart-input-container">
                <input type="text" 
                       id="${fieldName}-input"
                       placeholder="${placeholder}"
                       class="w-full bg-gray-800 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <div class="mt-1 text-sm text-red-400 hidden" id="${fieldName}-error"></div>
            </div>
        `;

        // 添加实时验证
        setTimeout(() => {
            const input = document.getElementById(`${fieldName}-input`);
            const error = document.getElementById(`${fieldName}-error`);

            input.addEventListener('input', (e) => {
                const result = this.validateField(fieldName, e.target.value);
                if (!result.valid) {
                    error.textContent = result.message;
                    error.classList.remove('hidden');
                    input.classList.add('ring-2', 'ring-red-500');
                } else {
                    error.classList.add('hidden');
                    input.classList.remove('ring-2', 'ring-red-500');
                }
            });
        }, 100);

        return inputHTML;
    }
}

// 初始化辅助功能
const voiceAssistant = new VoiceAssistant();
const dataVisualizer = new DataVisualizer();
const formValidator = new SmartFormValidator();
```

### 5. 响应式设计和主题切换

```css
/* 添加到 main.css */

/* 响应式设计 */
@media (max-width: 1024px) {
    .grid {
        grid-template-columns: 1fr !important;
    }
    
    #ai-agent-container {
        padding: 1rem;
    }
}

@media (max-width: 640px) {
    .quick-action-btn {
        flex-direction: column;
        padding: 0.75rem;
    }
    
    .quick-action-btn svg {
        margin-bottom: 0.25rem;
    }
}

/* 深色/浅色主题切换 */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f3f4f6;
    --bg-tertiary: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --border-color: #d1d5db;
}

[data-theme="light"] body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* 主题切换动画 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
```

### 6. 部署和优化建议

```markdown
## 性能优化建议

1. **代码分割**
   - 将AI逻辑、动画、工具类分离到不同文件
   - 使用动态导入按需加载功能

2. **资源优化**
   - 使用WebP格式图片
   - 启用Gzip压缩
   - 使用CDN加速静态资源

3. **用户体验优化**
   - 添加加载状态指示器
   - 实现离线缓存功能
   - 添加键盘快捷键支持

4. **安全考虑**
   - 实现CSRF保护
   - 使用HTTPS
   - 对敏感数据进行加密

## 扩展功能建议

1. **多语言支持**
   - 添加英文界面选项
   - 支持方言语音输入

2. **高级AI功能**
   - 情绪识别
   - 个性化推荐算法
   - 预测性建议

3. **数据分析仪表板**
   - 交易趋势图表
   - 费用分析报告
   - 使用习惯洞察

4. **集成功能**
   - 导出交易记录
   - 日历提醒集成
   - 第三方支付接入
```

## 使用指南

1. **在Cursor中创建项目**
   
   ```bash
   mkdir banking-ai-agent
   cd banking-ai-agent
   ```
1. **创建文件结构**
- 复制上述HTML到 `index.html`
- 创建 `styles/main.css` 并粘贴CSS代码
- 创建 `scripts/app.js` 并粘贴JavaScript代码
1. **启动开发服务器**
   
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve
   ```
1. **自定义和扩展**
- 根据实际需求调整UI组件
- 添加更多交互动画
- 集成真实的后端API
1. **测试关键流程**
- 测试三种主要服务（签约、修改、解约）
- 验证响应式设计
- 确保动画流畅性

## 演示要点

1. **开场展示AI主动性**
- 自动识别用户意图
- 智能推荐服务
1. **突出思维透明度**
- 展示AI思考过程
- 解释决策依据
1. **强调个性化体验**
- 根据用户数据定制方案
- 记住用户偏好
1. **展示安全保障**
- 多重身份验证
- 实时风险提示

这个指南将帮助您使用Cursor AI快速构建一个专业的银信通AI Agent演示网页。记住保持界面简洁、交互流畅，突出AI的智能特性。

```

```
/**
 * 演示控制器 - 演示系统的核心控制器
 * 
 * 负责管理演示流程、场景切换、演示参数控制等核心功能，
 * 是演示系统的中央调度器
 */

import { eventBus, DemoEvents } from './event-bus.js';
import { stateManager } from './state-manager.js';
import demoConfigModule from '../config/demo-config.js';
const { demo: demoConfig, scenarios: scenarioConfig } = demoConfigModule;

/**
 * 演示控制器类
 * 管理演示的生命周期和流程控制
 */
class DemoController {
    constructor() {
        // 当前演示场景
        this.currentScenario = null;
        
        // 演示定时器
        this.demoTimer = null;
        
        // 步骤定时器
        this.stepTimer = null;
        
        // 演示配置
        this.config = { ...demoConfig };
        
        // 是否已初始化
        this.initialized = false;
        
        // 初始化控制器
        this.init();
    }
    
    /**
     * 初始化演示控制器
     */
    init() {
        if (this.initialized) {
            return;
        }
        
        // 设置事件监听
        this._setupEventListeners();
        
        // 初始化键盘快捷键
        this._setupKeyboardShortcuts();
        
        // 加载默认场景
        this.loadScenario(this.config.defaultScenario);
        
        this.initialized = true;
        
        console.log('[DemoController] Initialized');
    }
    
    /**
     * 加载演示场景
     * @param {string} scenarioId - 场景ID
     * @returns {Promise<boolean>} 是否加载成功
     */
    async loadScenario(scenarioId) {
        try {
            // 检查场景是否存在
            if (!scenarioConfig[scenarioId]) {
                throw new Error(`Scenario not found: ${scenarioId}`);
            }
            
            // 停止当前演示
            if (this.isRunning()) {
                this.stopDemo();
            }
            
            // 加载场景配置
            this.currentScenario = {
                id: scenarioId,
                config: scenarioConfig[scenarioId],
                currentStepIndex: 0,
                startTime: null,
                elapsedTime: 0
            };
            
            // 更新状态
            stateManager.updateState({
                'demo.currentScenario': scenarioId,
                'demo.totalSteps': this.currentScenario.config.steps.length,
                'demo.stepIndex': 0,
                'demo.currentStep': null
            });
            
            // 触发场景加载事件
            eventBus.emit(DemoEvents.SCENARIO_LOADED, {
                scenarioId,
                scenario: this.currentScenario,
                totalSteps: this.currentScenario.config.steps.length
            });
            
            console.log(`[DemoController] Loaded scenario: ${scenarioId}`);
            return true;
            
        } catch (error) {
            console.error('[DemoController] Failed to load scenario:', error);
            return false;
        }
    }
    
    /**
     * 开始演示
     * @param {Object} options - 演示选项
     */
    startDemo(options = {}) {
        if (!this.currentScenario) {
            console.error('[DemoController] No scenario loaded');
            return;
        }
        
        if (this.isRunning()) {
            console.warn('[DemoController] Demo is already running');
            return;
        }
        
        // 应用演示选项
        if (options.mode) {
            this.setDemoMode(options.mode);
        }
        if (options.speed) {
            this.setDemoSpeed(options.speed);
        }
        
        // 重置演示状态
        this.currentScenario.currentStepIndex = 0;
        this.currentScenario.startTime = Date.now();
        this.currentScenario.elapsedTime = 0;
        
        // 更新状态
        stateManager.updateState({
            'demo.isRunning': true,
            'demo.isPaused': false,
            'demo.stepIndex': 0,
            'demo.startTime': this.currentScenario.startTime
        });
        
        // 触发演示开始事件
        eventBus.emit(DemoEvents.DEMO_STARTED, {
            scenarioId: this.currentScenario.id,
            mode: this.config.demoMode,
            speed: this.config.animationSpeed
        });
        
        // 开始第一步
        this._executeNextStep();
        
        console.log('[DemoController] Demo started');
    }
    
    /**
     * 暂停演示
     */
    pauseDemo() {
        if (!this.isRunning() || this.isPaused()) {
            return;
        }
        
        // 清除定时器
        this._clearTimers();
        
        // 更新状态
        stateManager.updateState('demo.isPaused', true);
        
        // 触发暂停事件
        eventBus.emit(DemoEvents.DEMO_PAUSED, {
            scenarioId: this.currentScenario.id,
            currentStep: this.getCurrentStep()
        });
        
        console.log('[DemoController] Demo paused');
    }
    
    /**
     * 恢复演示
     */
    resumeDemo() {
        if (!this.isRunning() || !this.isPaused()) {
            return;
        }
        
        // 更新状态
        stateManager.updateState('demo.isPaused', false);
        
        // 触发恢复事件
        eventBus.emit(DemoEvents.DEMO_RESUMED, {
            scenarioId: this.currentScenario.id,
            currentStep: this.getCurrentStep()
        });
        
        // 继续当前步骤
        this._continueCurrentStep();
        
        console.log('[DemoController] Demo resumed');
    }
    
    /**
     * 停止演示
     */
    stopDemo() {
        if (!this.isRunning()) {
            return;
        }
        
        // 清除定时器
        this._clearTimers();
        
        // 更新状态
        stateManager.updateState({
            'demo.isRunning': false,
            'demo.isPaused': false,
            'demo.currentStep': null
        });
        
        // 触发停止事件
        eventBus.emit(DemoEvents.DEMO_STOPPED, {
            scenarioId: this.currentScenario.id,
            elapsedTime: this.getElapsedTime()
        });
        
        console.log('[DemoController] Demo stopped');
    }
    
    /**
     * 重置演示
     */
    resetDemo() {
        // 停止当前演示
        this.stopDemo();
        
        // 重置场景状态
        if (this.currentScenario) {
            this.currentScenario.currentStepIndex = 0;
            this.currentScenario.startTime = null;
            this.currentScenario.elapsedTime = 0;
        }
        
        // 重置状态管理器
        stateManager.resetState('demo');
        stateManager.resetState('ai');
        stateManager.resetState('business');
        
        // 触发重置事件
        eventBus.emit(DemoEvents.DEMO_RESET, {
            scenarioId: this.currentScenario?.id
        });
        
        console.log('[DemoController] Demo reset');
    }
    
    /**
     * 跳转到指定步骤
     * @param {number} stepIndex - 步骤索引
     */
    skipToStep(stepIndex) {
        if (!this.currentScenario || stepIndex < 0 || stepIndex >= this.currentScenario.config.steps.length) {
            console.error('[DemoController] Invalid step index:', stepIndex);
            return;
        }
        
        // 清除当前定时器
        this._clearTimers();
        
        // 更新步骤索引
        this.currentScenario.currentStepIndex = stepIndex;
        
        // 更新状态
        stateManager.updateState('demo.stepIndex', stepIndex);
        
        // 如果演示正在运行，执行新步骤
        if (this.isRunning() && !this.isPaused()) {
            this._executeCurrentStep();
        }
        
        console.log(`[DemoController] Skipped to step: ${stepIndex}`);
    }
    
    /**
     * 执行下一步
     */
    nextStep() {
        if (!this.currentScenario) {
            return;
        }
        
        const nextIndex = this.currentScenario.currentStepIndex + 1;
        if (nextIndex < this.currentScenario.config.steps.length) {
            this.skipToStep(nextIndex);
        } else {
            // 演示完成
            this._completeDemo();
        }
    }
    
    /**
     * 执行上一步
     */
    prevStep() {
        if (!this.currentScenario) {
            return;
        }
        
        const prevIndex = this.currentScenario.currentStepIndex - 1;
        if (prevIndex >= 0) {
            this.skipToStep(prevIndex);
        }
    }
    
    /**
     * 设置演示模式
     * @param {string} mode - 演示模式 ('auto' | 'guided' | 'interactive')
     */
    setDemoMode(mode) {
        if (!['auto', 'guided', 'interactive'].includes(mode)) {
            console.error('[DemoController] Invalid demo mode:', mode);
            return;
        }
        
        this.config.demoMode = mode;
        stateManager.updateState('demo.mode', mode);
        
        eventBus.emit(DemoEvents.DEMO_MODE_CHANGED, { mode });
        
        console.log(`[DemoController] Demo mode changed to: ${mode}`);
    }
    
    /**
     * 设置演示速度
     * @param {string} speed - 演示速度 ('slow' | 'normal' | 'fast')
     */
    setDemoSpeed(speed) {
        if (!['slow', 'normal', 'fast'].includes(speed)) {
            console.error('[DemoController] Invalid demo speed:', speed);
            return;
        }
        
        this.config.animationSpeed = speed;
        stateManager.updateState('demo.speed', speed);
        
        eventBus.emit(DemoEvents.ANIMATION_SPEED_CHANGED, { speed });
        
        console.log(`[DemoController] Demo speed changed to: ${speed}`);
    }
    
    /**
     * 获取当前步骤
     * @returns {Object|null} 当前步骤配置
     */
    getCurrentStep() {
        if (!this.currentScenario) {
            return null;
        }
        
        const stepIndex = this.currentScenario.currentStepIndex;
        return this.currentScenario.config.steps[stepIndex] || null;
    }
    
    /**
     * 获取演示进度
     * @returns {number} 进度百分比 (0-100)
     */
    getProgress() {
        if (!this.currentScenario) {
            return 0;
        }
        
        const totalSteps = this.currentScenario.config.steps.length;
        const currentStep = this.currentScenario.currentStepIndex;
        
        return Math.round((currentStep / totalSteps) * 100);
    }
    
    /**
     * 获取已用时间
     * @returns {number} 已用时间（毫秒）
     */
    getElapsedTime() {
        if (!this.currentScenario || !this.currentScenario.startTime) {
            return 0;
        }
        
        if (this.isRunning()) {
            return Date.now() - this.currentScenario.startTime;
        } else {
            return this.currentScenario.elapsedTime;
        }
    }
    
    /**
     * 检查演示是否正在运行
     * @returns {boolean}
     */
    isRunning() {
        return stateManager.getState('demo.isRunning') || false;
    }
    
    /**
     * 检查演示是否已暂停
     * @returns {boolean}
     */
    isPaused() {
        return stateManager.getState('demo.isPaused') || false;
    }
    
    /**
     * 执行下一步
     * @private
     */
    _executeNextStep() {
        if (!this.currentScenario) {
            return;
        }
        
        const stepIndex = this.currentScenario.currentStepIndex;
        const totalSteps = this.currentScenario.config.steps.length;
        
        if (stepIndex >= totalSteps) {
            this._completeDemo();
            return;
        }
        
        this._executeCurrentStep();
    }
    
    /**
     * 执行当前步骤
     * @private
     */
    _executeCurrentStep() {
        const step = this.getCurrentStep();
        if (!step) {
            return;
        }
        
        // 更新状态
        stateManager.updateState({
            'demo.currentStep': step.id,
            'demo.stepIndex': this.currentScenario.currentStepIndex
        });
        
        // 触发步骤开始事件
        eventBus.emit(DemoEvents.STEP_STARTED, {
            stepId: step.id,
            stepIndex: this.currentScenario.currentStepIndex,
            step: step
        });
        
        // 根据演示模式处理步骤
        if (this.config.demoMode === 'auto' && step.autoAdvance) {
            // 自动模式：设置定时器自动推进
            this._scheduleNextStep(step.duration);
        }
        
        console.log(`[DemoController] Executing step: ${step.id}`);
    }
    
    /**
     * 继续当前步骤
     * @private
     */
    _continueCurrentStep() {
        const step = this.getCurrentStep();
        if (!step) {
            return;
        }
        
        // 如果是自动推进步骤，重新设置定时器
        if (this.config.demoMode === 'auto' && step.autoAdvance) {
            this._scheduleNextStep(step.duration);
        }
    }
    
    /**
     * 安排下一步执行
     * @private
     */
    _scheduleNextStep(delay) {
        this._clearStepTimer();
        
        this.stepTimer = setTimeout(() => {
            this._completeCurrentStep();
            this.currentScenario.currentStepIndex++;
            this._executeNextStep();
        }, delay);
    }
    
    /**
     * 完成当前步骤
     * @private
     */
    _completeCurrentStep() {
        const step = this.getCurrentStep();
        if (!step) {
            return;
        }
        
        // 触发步骤完成事件
        eventBus.emit(DemoEvents.STEP_COMPLETED, {
            stepId: step.id,
            stepIndex: this.currentScenario.currentStepIndex,
            step: step
        });
        
        console.log(`[DemoController] Completed step: ${step.id}`);
    }
    
    /**
     * 完成演示
     * @private
     */
    _completeDemo() {
        // 记录结束时间
        this.currentScenario.elapsedTime = this.getElapsedTime();
        
        // 触发场景完成事件
        eventBus.emit(DemoEvents.SCENARIO_COMPLETED, {
            scenarioId: this.currentScenario.id,
            elapsedTime: this.currentScenario.elapsedTime,
            totalSteps: this.currentScenario.config.steps.length
        });
        
        // 停止演示
        this.stopDemo();
        
        console.log(`[DemoController] Demo completed: ${this.currentScenario.id}`);
    }
    
    /**
     * 清除所有定时器
     * @private
     */
    _clearTimers() {
        this._clearStepTimer();
        this._clearDemoTimer();
    }
    
    /**
     * 清除步骤定时器
     * @private
     */
    _clearStepTimer() {
        if (this.stepTimer) {
            clearTimeout(this.stepTimer);
            this.stepTimer = null;
        }
    }
    
    /**
     * 清除演示定时器
     * @private
     */
    _clearDemoTimer() {
        if (this.demoTimer) {
            clearTimeout(this.demoTimer);
            this.demoTimer = null;
        }
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听用户交互事件，在交互模式下推进步骤
        eventBus.on(DemoEvents.USER_INTERACTION, (event) => {
            if (this.config.demoMode === 'interactive' && this.isRunning() && !this.isPaused()) {
                // 在交互模式下，用户交互可以推进演示
                const step = this.getCurrentStep();
                if (step && !step.autoAdvance) {
                    this._completeCurrentStep();
                    this.currentScenario.currentStepIndex++;
                    this._executeNextStep();
                }
            }
        });
    }
    
    /**
     * 设置键盘快捷键
     * @private
     */
    _setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // 只在没有输入框聚焦时响应快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                return;
            }
            
            switch (event.code) {
                case 'Space':
                    event.preventDefault();
                    if (this.isRunning()) {
                        if (this.isPaused()) {
                            this.resumeDemo();
                        } else {
                            this.pauseDemo();
                        }
                    } else {
                        this.startDemo();
                    }
                    break;
                    
                case 'ArrowRight':
                    event.preventDefault();
                    this.nextStep();
                    break;
                    
                case 'ArrowLeft':
                    event.preventDefault();
                    this.prevStep();
                    break;
                    
                case 'KeyR':
                    if (event.ctrlKey || event.metaKey) {
                        event.preventDefault();
                        this.resetDemo();
                    }
                    break;
            }
        });
    }
}

// 创建全局演示控制器实例
export const demoController = new DemoController();

// 导出演示控制器类
export default DemoController;

/**
 * 主应用控制器 - 银行AI智能柜员机演示系统
 * 
 * 这是演示系统的主入口，负责初始化所有核心模块，
 * 协调各个模块之间的交互，并提供统一的API接口
 */

import { eventBus, DemoEvents } from './event-bus.js';
import { stateManager } from './state-manager.js';
import { demoController } from './demo-controller.js';
import demoConfigModule from '../config/demo-config.js';

/**
 * 主应用类
 * 管理整个演示系统的生命周期和模块协调
 */
class BankingAIApp {
    constructor() {
        // 应用配置
        this.config = demoConfigModule;
        
        // 应用状态
        this.isInitialized = false;
        this.isReady = false;
        
        // DOM元素引用
        this.elements = {};
        
        // 模块引用
        this.modules = {
            eventBus,
            stateManager,
            demoController
        };
        
        // 绑定方法上下文
        this._bindMethods();
    }
    
    /**
     * 初始化应用
     * @param {Object} options - 初始化选项
     */
    async init(options = {}) {
        if (this.isInitialized) {
            console.warn('[BankingAIApp] App already initialized');
            return;
        }
        
        try {
            console.log('[BankingAIApp] Initializing...');
            
            // 合并配置
            this._mergeConfig(options);
            
            // 初始化DOM元素
            this._initializeElements();
            
            // 设置事件监听
            this._setupEventListeners();
            
            // 初始化UI
            this._initializeUI();
            
            // 设置全局错误处理
            this._setupErrorHandling();
            
            // 标记为已初始化
            this.isInitialized = true;
            
            // 触发初始化完成事件
            eventBus.emit('app:initialized', { app: this });
            
            console.log('[BankingAIApp] Initialization completed');
            
            // 自动开始演示（如果配置了）
            if (this.config.demo.autoStart) {
                await this.startDemo();
            }
            
        } catch (error) {
            console.error('[BankingAIApp] Initialization failed:', error);
            throw error;
        }
    }
    
    /**
     * 开始演示
     * @param {string} scenarioId - 可选，指定场景ID
     * @param {Object} options - 演示选项
     */
    async startDemo(scenarioId = null, options = {}) {
        try {
            // 如果指定了场景，先加载场景
            if (scenarioId) {
                await demoController.loadScenario(scenarioId);
            }
            
            // 开始演示
            demoController.startDemo(options);
            
            // 显示欢迎消息
            this._showWelcomeMessage();
            
        } catch (error) {
            console.error('[BankingAIApp] Failed to start demo:', error);
            this._showErrorMessage('演示启动失败，请刷新页面重试');
        }
    }
    
    /**
     * 停止演示
     */
    stopDemo() {
        demoController.stopDemo();
    }
    
    /**
     * 重置演示
     */
    resetDemo() {
        demoController.resetDemo();
        this._clearMessages();
        this._showWelcomeMessage();
    }
    
    /**
     * 切换演示场景
     * @param {string} scenarioId - 场景ID
     */
    async switchScenario(scenarioId) {
        try {
            await demoController.loadScenario(scenarioId);
            this._clearMessages();
            this._showScenarioInfo(scenarioId);
        } catch (error) {
            console.error('[BankingAIApp] Failed to switch scenario:', error);
            this._showErrorMessage('场景切换失败');
        }
    }
    
    /**
     * 发送用户消息
     * @param {string} message - 用户消息
     */
    sendUserMessage(message) {
        if (!message || !message.trim()) {
            return;
        }
        
        // 添加用户消息到界面
        this._addUserMessage(message.trim());
        
        // 记录用户交互
        stateManager.addUserInteraction({
            type: 'message',
            content: message.trim(),
            source: 'user_input'
        });
        
        // 触发用户输入事件
        eventBus.emit(DemoEvents.USER_INPUT, {
            message: message.trim(),
            timestamp: Date.now()
        });
        
        // 清空输入框
        const userInput = this.elements.userInput;
        if (userInput) {
            userInput.value = '';
        }
    }
    
    /**
     * 添加AI消息
     * @param {string} content - 消息内容
     * @param {Object} options - 选项
     */
    addAIMessage(content, options = {}) {
        this._addAIMessage(content, options);
        
        // 记录AI对话
        stateManager.addConversationMessage({
            type: 'ai',
            content,
            options
        });
    }
    
    /**
     * 显示AI思维过程
     * @param {Array} steps - 思维步骤
     */
    async showAIThinking(steps) {
        if (!Array.isArray(steps) || steps.length === 0) {
            return;
        }
        
        // 触发AI思维开始事件
        eventBus.emit(DemoEvents.AI_THINKING_START, { steps });
        
        // 显示思维容器
        this._showThinkingContainer();
        
        // 逐步显示思维过程
        for (let i = 0; i < steps.length; i++) {
            await this._addThinkingStep(steps[i], i);
            
            // 触发思维步骤事件
            eventBus.emit(DemoEvents.AI_THINKING_STEP, {
                step: steps[i],
                index: i,
                total: steps.length
            });
            
            // 等待动画完成
            await this._delay(this._getThinkingStepDelay());
        }
        
        // 触发AI思维结束事件
        eventBus.emit(DemoEvents.AI_THINKING_END, { steps });
        
        // 延迟后隐藏思维容器
        setTimeout(() => {
            this._hideThinkingContainer();
        }, 2000);
    }
    
    /**
     * 切换主题
     * @param {string} theme - 主题名称 ('dark' | 'light')
     */
    toggleTheme(theme = null) {
        const currentTheme = stateManager.getState('ui.theme');
        const newTheme = theme || (currentTheme === 'dark' ? 'light' : 'dark');
        
        // 更新状态
        stateManager.updateState('ui.theme', newTheme);
        
        // 更新DOM
        document.documentElement.setAttribute('data-theme', newTheme);
        
        // 触发主题变更事件
        eventBus.emit(DemoEvents.UI_THEME_CHANGED, { theme: newTheme });
        
        console.log(`[BankingAIApp] Theme changed to: ${newTheme}`);
    }
    
    /**
     * 获取应用状态
     * @param {string} path - 状态路径
     * @returns {*} 状态值
     */
    getState(path = null) {
        return stateManager.getState(path);
    }
    
    /**
     * 更新应用状态
     * @param {string|Object} pathOrState - 状态路径或状态对象
     * @param {*} value - 新值
     */
    setState(pathOrState, value = null) {
        stateManager.updateState(pathOrState, value);
    }
    
    /**
     * 订阅状态变更
     * @param {string} path - 状态路径
     * @param {Function} callback - 回调函数
     * @returns {Function} 取消订阅函数
     */
    subscribe(path, callback) {
        return stateManager.subscribe(path, callback);
    }
    
    /**
     * 销毁应用
     */
    destroy() {
        // 停止演示
        this.stopDemo();
        
        // 清除事件监听
        this._removeEventListeners();
        
        // 清理DOM
        this._cleanup();
        
        // 重置状态
        this.isInitialized = false;
        this.isReady = false;
        
        console.log('[BankingAIApp] App destroyed');
    }
    
    /**
     * 绑定方法上下文
     * @private
     */
    _bindMethods() {
        this.sendUserMessage = this.sendUserMessage.bind(this);
        this.toggleTheme = this.toggleTheme.bind(this);
        this._handleKeyPress = this._handleKeyPress.bind(this);
        this._handleSendClick = this._handleSendClick.bind(this);
    }
    
    /**
     * 合并配置
     * @private
     */
    _mergeConfig(options) {
        if (options.demo) {
            Object.assign(this.config.demo, options.demo);
        }
        if (options.ui) {
            Object.assign(this.config.ui, options.ui);
        }
    }
    
    /**
     * 初始化DOM元素
     * @private
     */
    _initializeElements() {
        this.elements = {
            chatMessages: document.getElementById('chat-messages'),
            userInput: document.getElementById('user-input'),
            sendButton: document.getElementById('send-button'),
            themeToggle: document.getElementById('theme-toggle'),
            demoControls: document.getElementById('demo-controls'),
            progressBar: document.getElementById('progress-bar'),
            stepIndicator: document.getElementById('step-indicator')
        };
        
        // 检查必需元素
        const requiredElements = ['chatMessages', 'userInput', 'sendButton'];
        for (const elementName of requiredElements) {
            if (!this.elements[elementName]) {
                throw new Error(`Required element not found: ${elementName}`);
            }
        }
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 用户输入事件
        if (this.elements.userInput) {
            this.elements.userInput.addEventListener('keypress', this._handleKeyPress);
        }
        
        // 发送按钮事件
        if (this.elements.sendButton) {
            this.elements.sendButton.addEventListener('click', this._handleSendClick);
        }
        
        // 主题切换事件
        if (this.elements.themeToggle) {
            this.elements.themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // 演示控制事件
        this._setupDemoControlEvents();
        
        // 应用级事件监听
        this._setupAppEventListeners();
    }
    
    /**
     * 设置演示控制事件
     * @private
     */
    _setupDemoControlEvents() {
        // 监听演示状态变更
        eventBus.on(DemoEvents.DEMO_STARTED, () => {
            this._updateDemoControls('running');
        });
        
        eventBus.on(DemoEvents.DEMO_PAUSED, () => {
            this._updateDemoControls('paused');
        });
        
        eventBus.on(DemoEvents.DEMO_STOPPED, () => {
            this._updateDemoControls('stopped');
        });
        
        // 监听步骤变更
        eventBus.on(DemoEvents.STEP_STARTED, (event) => {
            this._updateProgress();
            this._updateStepIndicator(event.data);
        });
    }
    
    /**
     * 设置应用级事件监听
     * @private
     */
    _setupAppEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this._adjustLayout();
        });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && demoController.isRunning()) {
                demoController.pauseDemo();
            }
        });
    }
    
    /**
     * 初始化UI
     * @private
     */
    _initializeUI() {
        // 设置初始主题
        this.toggleTheme(this.config.ui.theme?.mode || 'dark');
        
        // 调整布局
        this._adjustLayout();
        
        // 显示演示控制面板
        if (this.config.demo.showDemoControls) {
            this._showDemoControls();
        }
        
        // 标记为就绪
        this.isReady = true;
    }
    
    /**
     * 设置错误处理
     * @private
     */
    _setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('[BankingAIApp] Global error:', event.error);
            this._showErrorMessage('系统发生错误，请刷新页面重试');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('[BankingAIApp] Unhandled promise rejection:', event.reason);
            this._showErrorMessage('系统发生错误，请刷新页面重试');
        });
    }
    
    /**
     * 处理键盘按键
     * @private
     */
    _handleKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendUserMessage(this.elements.userInput.value);
        }
    }
    
    /**
     * 处理发送按钮点击
     * @private
     */
    _handleSendClick() {
        this.sendUserMessage(this.elements.userInput.value);
    }
    
    /**
     * 延迟函数
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 获取思维步骤延迟时间
     * @private
     */
    _getThinkingStepDelay() {
        const speed = this.config.demo.animationSpeed;
        const baseDelay = this.config.demo.thinkingChainSpeed || 800;

        switch (speed) {
            case 'slow': return baseDelay * 1.5;
            case 'fast': return baseDelay * 0.5;
            default: return baseDelay;
        }
    }

    /**
     * 显示欢迎消息
     * @private
     */
    _showWelcomeMessage() {
        const welcomeContent = `
            <div class="bg-gradient-to-r from-blue-900/30 to-purple-900/30 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-blue-400">🤖</span>
                    <span class="font-semibold text-blue-400">银行AI智能助手</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">
                    您好！我是您的专属银行AI助手，可以帮您办理各种银行业务。
                </p>
                <div class="text-xs text-gray-400">
                    💡 提示：您可以说"我要开通银信通"或点击下方推荐服务
                </div>
            </div>
        `;

        this._addAIMessage(welcomeContent);
    }

    /**
     * 显示场景信息
     * @private
     */
    _showScenarioInfo(scenarioId) {
        const scenario = this.config.scenarios[scenarioId];
        if (!scenario) return;

        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-green-400">🎬</span>
                    <span class="font-semibold text-green-400">演示场景</span>
                </div>
                <h4 class="font-medium text-gray-200 mb-2">${scenario.name}</h4>
                <p class="text-sm text-gray-300 mb-3">${scenario.description}</p>
                <div class="text-xs text-gray-400">
                    预计时长：${Math.round(scenario.duration / 60)}分钟 |
                    步骤数：${scenario.steps.length}
                </div>
            </div>
        `;

        this._addAIMessage(content);
    }

    /**
     * 显示错误消息
     * @private
     */
    _showErrorMessage(message) {
        const content = `
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-red-400">⚠️</span>
                    <span class="font-semibold text-red-400">系统提示</span>
                </div>
                <p class="text-sm text-gray-300">${message}</p>
            </div>
        `;

        this._addAIMessage(content);
    }

    /**
     * 添加用户消息到界面
     * @private
     */
    _addUserMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'user-message mb-4';
        messageDiv.innerHTML = `
            <div class="flex justify-end">
                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-blue-600 text-white">
                    ${this._escapeHtml(message)}
                </div>
            </div>
        `;

        this.elements.chatMessages.appendChild(messageDiv);
        this._scrollToBottom();
    }

    /**
     * 添加AI消息到界面
     * @private
     */
    _addAIMessage(content, options = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'ai-message mb-4';
        messageDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span class="text-white text-sm font-bold">AI</span>
                </div>
                <div class="flex-1 rounded-2xl p-4 bg-gray-800/50 border border-gray-700/50">
                    ${content}
                </div>
            </div>
        `;

        this.elements.chatMessages.appendChild(messageDiv);
        this._scrollToBottom();

        // 添加动画效果
        if (options.animate !== false) {
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateY(20px)';

            requestAnimationFrame(() => {
                messageDiv.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translateY(0)';
            });
        }
    }

    /**
     * 显示思维容器
     * @private
     */
    _showThinkingContainer() {
        // 如果已存在思维容器，先移除
        this._hideThinkingContainer();

        const thinkingDiv = document.createElement('div');
        thinkingDiv.id = 'ai-thinking-container';
        thinkingDiv.className = 'ai-message mb-4';
        thinkingDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span class="text-white text-sm font-bold">AI</span>
                </div>
                <div class="flex-1 rounded-2xl p-4 bg-gray-800/50 border border-gray-700/50">
                    <div class="flex items-center space-x-2 mb-3">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-75"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                        </div>
                        <span class="text-sm text-gray-400">AI 正在思考...</span>
                    </div>
                    <div id="thinking-steps" class="space-y-2 text-sm text-gray-300">
                        <!-- 思维步骤将在这里动态添加 -->
                    </div>
                </div>
            </div>
        `;

        this.elements.chatMessages.appendChild(thinkingDiv);
        this._scrollToBottom();
    }

    /**
     * 隐藏思维容器
     * @private
     */
    _hideThinkingContainer() {
        const container = document.getElementById('ai-thinking-container');
        if (container) {
            container.remove();
        }
    }

    /**
     * 添加思维步骤
     * @private
     */
    async _addThinkingStep(step, index) {
        const stepsContainer = document.getElementById('thinking-steps');
        if (!stepsContainer) return;

        const stepDiv = document.createElement('div');
        stepDiv.className = 'thinking-step opacity-0 transform translate-y-2';
        stepDiv.innerHTML = `
            <div class="flex items-center space-x-2">
                <span class="text-blue-400">▸</span>
                <span>${this._escapeHtml(step.text || step)}</span>
                ${step.confidence ? `<span class="text-xs text-gray-500">(${step.confidence}%)</span>` : ''}
            </div>
        `;

        stepsContainer.appendChild(stepDiv);

        // 动画显示
        await this._delay(50);
        stepDiv.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        stepDiv.style.opacity = '1';
        stepDiv.style.transform = 'translateY(0)';

        this._scrollToBottom();
    }

    /**
     * 清空消息
     * @private
     */
    _clearMessages() {
        if (this.elements.chatMessages) {
            this.elements.chatMessages.innerHTML = '';
        }
    }

    /**
     * 滚动到底部
     * @private
     */
    _scrollToBottom() {
        if (this.elements.chatMessages) {
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
        }
    }

    /**
     * 转义HTML
     * @private
     */
    _escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 更新演示控制按钮
     * @private
     */
    _updateDemoControls(state) {
        // 这里可以根据演示状态更新控制按钮的显示
        console.log(`[BankingAIApp] Demo state changed to: ${state}`);
    }

    /**
     * 更新进度条
     * @private
     */
    _updateProgress() {
        const progress = demoController.getProgress();
        if (this.elements.progressBar) {
            this.elements.progressBar.style.width = `${progress}%`;
        }
    }

    /**
     * 更新步骤指示器
     * @private
     */
    _updateStepIndicator(stepData) {
        if (this.elements.stepIndicator) {
            this.elements.stepIndicator.textContent = `步骤 ${stepData.stepIndex + 1}/${stepData.totalSteps || '?'}`;
        }
    }

    /**
     * 显示演示控制面板
     * @private
     */
    _showDemoControls() {
        // 实现演示控制面板的显示逻辑
        console.log('[BankingAIApp] Demo controls shown');
    }

    /**
     * 调整布局
     * @private
     */
    _adjustLayout() {
        // 实现响应式布局调整
        console.log('[BankingAIApp] Layout adjusted');
    }

    /**
     * 移除事件监听
     * @private
     */
    _removeEventListeners() {
        // 移除所有事件监听器
        if (this.elements.userInput) {
            this.elements.userInput.removeEventListener('keypress', this._handleKeyPress);
        }
        if (this.elements.sendButton) {
            this.elements.sendButton.removeEventListener('click', this._handleSendClick);
        }
    }

    /**
     * 清理资源
     * @private
     */
    _cleanup() {
        // 清理DOM和其他资源
        this.elements = {};
    }
}

// 导出应用类和全局实例
export default BankingAIApp;

// 创建全局应用实例（将在页面加载后初始化）
export let bankingAIApp = null;

// 初始化函数（供HTML页面调用）
export async function initializeApp(options = {}) {
    if (bankingAIApp) {
        console.warn('[BankingAIApp] App already exists');
        return bankingAIApp;
    }
    
    bankingAIApp = new BankingAIApp();
    await bankingAIApp.init(options);
    
    // 将应用实例暴露到全局（用于调试和HTML调用）
    window.bankingAIApp = bankingAIApp;
    
    return bankingAIApp;
}

/**
 * 状态管理器 - 演示系统的状态管理中心
 * 
 * 负责管理演示系统的全局状态，包括演示状态、用户状态、
 * 业务状态等，并提供状态变更的监听和通知机制
 */

import { eventBus, DemoEvents } from './event-bus.js';

/**
 * 状态管理器类
 * 实现集中式状态管理，支持状态变更监听和历史记录
 */
class StateManager {
    constructor() {
        // 初始化状态
        this.state = this._getInitialState();
        
        // 状态变更历史
        this.stateHistory = [];
        
        // 状态监听器
        this.stateListeners = new Map();
        
        // 最大历史记录数量
        this.maxHistorySize = 50;
        
        // 是否启用调试模式
        this.debugMode = false;
        
        // 初始化状态监听
        this._initializeStateListeners();
    }
    
    /**
     * 获取初始状态
     * @private
     * @returns {Object} 初始状态对象
     */
    _getInitialState() {
        return {
            // 演示状态
            demo: {
                isRunning: false,
                isPaused: false,
                currentScenario: null,
                currentStep: null,
                stepIndex: 0,
                totalSteps: 0,
                mode: 'guided', // 'auto' | 'guided' | 'interactive'
                speed: 'normal', // 'slow' | 'normal' | 'fast'
                startTime: null,
                elapsedTime: 0
            },
            
            // 用户状态
            user: {
                profile: {
                    name: '张先生',
                    cardNumber: '8899',
                    cardType: '工资卡',
                    phoneNumber: '138****8888',
                    hasYinxintong: false
                },
                preferences: {
                    theme: 'dark',
                    language: 'zh-CN',
                    animationSpeed: 'normal',
                    showConfidence: true
                },
                interactions: []
            },
            
            // 业务状态
            business: {
                currentService: null,
                serviceData: {},
                recommendations: [],
                selectedSolution: null,
                executionProgress: {
                    currentStep: 0,
                    totalSteps: 0,
                    isExecuting: false,
                    completedSteps: []
                }
            },
            
            // AI状态
            ai: {
                isThinking: false,
                thinkingSteps: [],
                currentThinkingStep: 0,
                confidence: 0,
                lastDecision: null,
                conversationHistory: []
            },
            
            // UI状态
            ui: {
                theme: 'dark',
                sidebarOpen: false,
                controlPanelVisible: true,
                chatScrollPosition: 0,
                activeModal: null,
                notifications: []
            },
            
            // 配置状态
            config: {
                demo: {},
                business: {},
                ui: {}
            }
        };
    }
    
    /**
     * 初始化状态监听器
     * @private
     */
    _initializeStateListeners() {
        // 监听演示控制事件
        eventBus.on(DemoEvents.DEMO_STARTED, (event) => {
            this.updateState('demo.isRunning', true);
            this.updateState('demo.startTime', Date.now());
        });
        
        eventBus.on(DemoEvents.DEMO_PAUSED, () => {
            this.updateState('demo.isPaused', true);
        });
        
        eventBus.on(DemoEvents.DEMO_RESUMED, () => {
            this.updateState('demo.isPaused', false);
        });
        
        eventBus.on(DemoEvents.DEMO_STOPPED, () => {
            this.updateState('demo.isRunning', false);
            this.updateState('demo.isPaused', false);
        });
        
        // 监听场景变更事件
        eventBus.on(DemoEvents.SCENARIO_CHANGED, (event) => {
            this.updateState('demo.currentScenario', event.data.scenarioId);
            this.updateState('demo.stepIndex', 0);
            this.updateState('demo.totalSteps', event.data.totalSteps || 0);
        });
        
        // 监听步骤变更事件
        eventBus.on(DemoEvents.STEP_STARTED, (event) => {
            this.updateState('demo.currentStep', event.data.stepId);
            this.updateState('demo.stepIndex', event.data.stepIndex);
        });
        
        // 监听AI思维事件
        eventBus.on(DemoEvents.AI_THINKING_START, () => {
            this.updateState('ai.isThinking', true);
            this.updateState('ai.currentThinkingStep', 0);
        });
        
        eventBus.on(DemoEvents.AI_THINKING_END, () => {
            this.updateState('ai.isThinking', false);
        });
        
        // 监听用户交互事件
        eventBus.on(DemoEvents.USER_INTERACTION, (event) => {
            this.addUserInteraction(event.data);
        });
    }
    
    /**
     * 获取状态值
     * @param {string} path - 状态路径，支持点号分隔的嵌套路径
     * @returns {*} 状态值
     */
    getState(path = null) {
        if (!path) {
            return this.state;
        }
        
        return this._getNestedValue(this.state, path);
    }
    
    /**
     * 更新状态
     * @param {string|Object} pathOrState - 状态路径或状态对象
     * @param {*} value - 新值（当第一个参数是路径时）
     * @param {Object} options - 选项
     */
    updateState(pathOrState, value = null, options = {}) {
        const oldState = JSON.parse(JSON.stringify(this.state));
        
        if (typeof pathOrState === 'string') {
            // 更新单个状态值
            this._setNestedValue(this.state, pathOrState, value);
        } else if (typeof pathOrState === 'object') {
            // 批量更新状态
            Object.keys(pathOrState).forEach(path => {
                this._setNestedValue(this.state, path, pathOrState[path]);
            });
        }
        
        // 记录状态变更历史
        this._recordStateChange(oldState, this.state, pathOrState, value);
        
        // 触发状态变更事件
        this._notifyStateChange(pathOrState, value, oldState);
        
        this._debug('State updated', { path: pathOrState, value, newState: this.state });
    }
    
    /**
     * 重置状态
     * @param {string} path - 可选，指定要重置的状态路径
     */
    resetState(path = null) {
        const initialState = this._getInitialState();
        
        if (path) {
            const initialValue = this._getNestedValue(initialState, path);
            this.updateState(path, initialValue);
        } else {
            this.state = initialState;
            this._notifyStateChange('*', null, null);
        }
        
        this._debug('State reset', { path });
    }
    
    /**
     * 订阅状态变更
     * @param {string} path - 状态路径，'*' 表示监听所有变更
     * @param {Function} callback - 回调函数
     * @returns {Function} 取消订阅函数
     */
    subscribe(path, callback) {
        if (!this.stateListeners.has(path)) {
            this.stateListeners.set(path, []);
        }
        
        this.stateListeners.get(path).push(callback);
        
        // 返回取消订阅函数
        return () => {
            const listeners = this.stateListeners.get(path);
            if (listeners) {
                const index = listeners.indexOf(callback);
                if (index !== -1) {
                    listeners.splice(index, 1);
                }
            }
        };
    }
    
    /**
     * 添加用户交互记录
     * @param {Object} interaction - 交互数据
     */
    addUserInteraction(interaction) {
        const interactions = this.getState('user.interactions');
        interactions.push({
            ...interaction,
            timestamp: Date.now(),
            id: Date.now().toString()
        });
        
        // 限制交互记录数量
        if (interactions.length > 100) {
            interactions.shift();
        }
        
        this.updateState('user.interactions', interactions);
    }
    
    /**
     * 添加AI对话记录
     * @param {Object} message - 对话消息
     */
    addConversationMessage(message) {
        const history = this.getState('ai.conversationHistory');
        history.push({
            ...message,
            timestamp: Date.now(),
            id: Date.now().toString()
        });
        
        // 限制对话记录数量
        if (history.length > 50) {
            history.shift();
        }
        
        this.updateState('ai.conversationHistory', history);
    }
    
    /**
     * 获取状态变更历史
     * @param {number} limit - 限制数量
     * @returns {Array} 状态变更历史
     */
    getStateHistory(limit = 20) {
        return this.stateHistory.slice(-limit);
    }
    
    /**
     * 清空状态历史
     */
    clearStateHistory() {
        this.stateHistory = [];
        this._debug('State history cleared');
    }
    
    /**
     * 启用/禁用调试模式
     * @param {boolean} enabled - 是否启用
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        this._debug(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * 获取嵌套对象的值
     * @private
     */
    _getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }
    
    /**
     * 设置嵌套对象的值
     * @private
     */
    _setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
    }
    
    /**
     * 记录状态变更
     * @private
     */
    _recordStateChange(oldState, newState, path, value) {
        this.stateHistory.push({
            timestamp: Date.now(),
            path,
            value,
            oldState: oldState,
            newState: JSON.parse(JSON.stringify(newState))
        });
        
        // 限制历史记录大小
        if (this.stateHistory.length > this.maxHistorySize) {
            this.stateHistory.shift();
        }
    }
    
    /**
     * 通知状态变更
     * @private
     */
    _notifyStateChange(path, value, oldState) {
        // 通知具体路径的监听器
        if (typeof path === 'string' && this.stateListeners.has(path)) {
            this.stateListeners.get(path).forEach(callback => {
                try {
                    callback(value, oldState);
                } catch (error) {
                    console.error('Error in state listener:', error);
                }
            });
        }
        
        // 通知全局监听器
        if (this.stateListeners.has('*')) {
            this.stateListeners.get('*').forEach(callback => {
                try {
                    callback(this.state, oldState);
                } catch (error) {
                    console.error('Error in global state listener:', error);
                }
            });
        }
    }
    
    /**
     * 调试日志
     * @private
     */
    _debug(message, data = null) {
        if (this.debugMode) {
            if (data) {
                console.log(`[StateManager] ${message}`, data);
            } else {
                console.log(`[StateManager] ${message}`);
            }
        }
    }
}

// 创建全局状态管理器实例
export const stateManager = new StateManager();

// 导出状态管理器类
export default StateManager;

/**
 * 事件总线 - 演示系统的事件管理中心
 * 
 * 负责管理演示系统中各个模块之间的事件通信，
 * 支持事件的发布、订阅和取消订阅
 */

// 演示事件类型定义
export const DemoEvents = {
    // 演示控制事件
    DEMO_STARTED: 'demo:started',
    DEMO_PAUSED: 'demo:paused',
    DEMO_RESUMED: 'demo:resumed',
    DEMO_STOPPED: 'demo:stopped',
    DEMO_RESET: 'demo:reset',
    
    // 场景事件
    SCENARIO_LOADED: 'scenario:loaded',
    SCENARIO_CHANGED: 'scenario:changed',
    SCENARIO_COMPLETED: 'scenario:completed',
    
    // 步骤事件
    STEP_STARTED: 'step:started',
    STEP_COMPLETED: 'step:completed',
    STEP_SKIPPED: 'step:skipped',
    
    // AI事件
    AI_THINKING_START: 'ai:thinking:start',
    AI_THINKING_STEP: 'ai:thinking:step',
    AI_THINKING_END: 'ai:thinking:end',
    AI_RECOMMENDATION_GENERATED: 'ai:recommendation:generated',
    AI_DECISION_MADE: 'ai:decision:made',
    
    // 用户交互事件
    USER_INPUT: 'user:input',
    USER_CLICK: 'user:click',
    USER_SELECTION: 'user:selection',
    USER_INTERACTION: 'user:interaction',
    
    // 业务事件
    BUSINESS_PROCESS_START: 'business:process:start',
    BUSINESS_PROCESS_STEP: 'business:process:step',
    BUSINESS_PROCESS_COMPLETE: 'business:process:complete',
    BUSINESS_PROCESS_ERROR: 'business:process:error',
    
    // UI事件
    UI_ANIMATION_START: 'ui:animation:start',
    UI_ANIMATION_END: 'ui:animation:end',
    UI_THEME_CHANGED: 'ui:theme:changed',
    UI_LAYOUT_CHANGED: 'ui:layout:changed',
    
    // 配置事件
    CONFIG_CHANGED: 'config:changed',
    DEMO_MODE_CHANGED: 'demo:mode:changed',
    ANIMATION_SPEED_CHANGED: 'animation:speed:changed'
};

/**
 * 事件总线类
 * 实现发布-订阅模式，管理演示系统的事件通信
 */
class EventBus {
    constructor() {
        // 事件监听器存储
        this.listeners = new Map();
        
        // 事件历史记录（用于调试）
        this.eventHistory = [];
        
        // 是否启用调试模式
        this.debugMode = false;
        
        // 最大历史记录数量
        this.maxHistorySize = 100;
    }
    
    /**
     * 订阅事件
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     * @param {Object} options - 选项
     * @returns {Function} 取消订阅函数
     */
    on(eventType, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('Event callback must be a function');
        }
        
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, []);
        }
        
        const listener = {
            callback,
            once: options.once || false,
            priority: options.priority || 0,
            context: options.context || null
        };
        
        const listeners = this.listeners.get(eventType);
        
        // 按优先级插入
        const insertIndex = listeners.findIndex(l => l.priority < listener.priority);
        if (insertIndex === -1) {
            listeners.push(listener);
        } else {
            listeners.splice(insertIndex, 0, listener);
        }
        
        this._debug(`Subscribed to event: ${eventType}`, { listener, totalListeners: listeners.length });
        
        // 返回取消订阅函数
        return () => this.off(eventType, callback);
    }
    
    /**
     * 订阅事件（仅触发一次）
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     * @param {Object} options - 选项
     * @returns {Function} 取消订阅函数
     */
    once(eventType, callback, options = {}) {
        return this.on(eventType, callback, { ...options, once: true });
    }
    
    /**
     * 取消订阅事件
     * @param {string} eventType - 事件类型
     * @param {Function} callback - 回调函数
     */
    off(eventType, callback) {
        if (!this.listeners.has(eventType)) {
            return;
        }
        
        const listeners = this.listeners.get(eventType);
        const index = listeners.findIndex(l => l.callback === callback);
        
        if (index !== -1) {
            listeners.splice(index, 1);
            this._debug(`Unsubscribed from event: ${eventType}`, { remainingListeners: listeners.length });
            
            // 如果没有监听器了，删除事件类型
            if (listeners.length === 0) {
                this.listeners.delete(eventType);
            }
        }
    }
    
    /**
     * 发布事件
     * @param {string} eventType - 事件类型
     * @param {*} data - 事件数据
     * @param {Object} options - 选项
     */
    emit(eventType, data = null, options = {}) {
        const eventData = {
            type: eventType,
            data,
            timestamp: Date.now(),
            source: options.source || 'unknown'
        };
        
        // 记录事件历史
        this._recordEvent(eventData);
        
        this._debug(`Emitting event: ${eventType}`, eventData);
        
        if (!this.listeners.has(eventType)) {
            this._debug(`No listeners for event: ${eventType}`);
            return;
        }
        
        const listeners = this.listeners.get(eventType);
        const listenersToRemove = [];
        
        // 执行所有监听器
        listeners.forEach((listener, index) => {
            try {
                if (listener.context) {
                    listener.callback.call(listener.context, eventData);
                } else {
                    listener.callback(eventData);
                }
                
                // 如果是一次性监听器，标记为需要移除
                if (listener.once) {
                    listenersToRemove.push(index);
                }
            } catch (error) {
                console.error(`Error in event listener for ${eventType}:`, error);
                this._debug(`Error in event listener: ${eventType}`, { error, listener });
            }
        });
        
        // 移除一次性监听器
        listenersToRemove.reverse().forEach(index => {
            listeners.splice(index, 1);
        });
        
        // 如果没有监听器了，删除事件类型
        if (listeners.length === 0) {
            this.listeners.delete(eventType);
        }
    }
    
    /**
     * 移除所有监听器
     * @param {string} eventType - 可选，指定事件类型
     */
    removeAllListeners(eventType = null) {
        if (eventType) {
            this.listeners.delete(eventType);
            this._debug(`Removed all listeners for event: ${eventType}`);
        } else {
            this.listeners.clear();
            this._debug('Removed all event listeners');
        }
    }
    
    /**
     * 获取事件监听器数量
     * @param {string} eventType - 可选，指定事件类型
     * @returns {number} 监听器数量
     */
    getListenerCount(eventType = null) {
        if (eventType) {
            return this.listeners.has(eventType) ? this.listeners.get(eventType).length : 0;
        } else {
            let total = 0;
            this.listeners.forEach(listeners => {
                total += listeners.length;
            });
            return total;
        }
    }
    
    /**
     * 获取所有事件类型
     * @returns {Array} 事件类型数组
     */
    getEventTypes() {
        return Array.from(this.listeners.keys());
    }
    
    /**
     * 获取事件历史
     * @param {number} limit - 限制数量
     * @returns {Array} 事件历史数组
     */
    getEventHistory(limit = 50) {
        return this.eventHistory.slice(-limit);
    }
    
    /**
     * 清空事件历史
     */
    clearEventHistory() {
        this.eventHistory = [];
        this._debug('Event history cleared');
    }
    
    /**
     * 启用/禁用调试模式
     * @param {boolean} enabled - 是否启用
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        this._debug(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * 记录事件到历史
     * @private
     */
    _recordEvent(eventData) {
        this.eventHistory.push(eventData);
        
        // 限制历史记录大小
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }
    
    /**
     * 调试日志
     * @private
     */
    _debug(message, data = null) {
        if (this.debugMode) {
            if (data) {
                console.log(`[EventBus] ${message}`, data);
            } else {
                console.log(`[EventBus] ${message}`);
            }
        }
    }
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 导出事件总线类（用于创建新实例）
export default EventBus;

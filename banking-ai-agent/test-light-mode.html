<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>明亮模式测试 - 智能银行助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary); color: var(--text-primary);">
    <!-- 明亮模式测试容器 -->
    <div class="min-h-screen flex flex-col">
        
        <!-- 顶部导航栏 -->
        <nav class="backdrop-blur-xl sticky top-0 z-50" style="background-color: var(--bg-primary); border-bottom: 1px solid var(--border-primary);">
            <div class="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-sm">AI</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-semibold" style="color: var(--text-primary);">明亮模式测试</h1>
                        <p class="text-xs" style="color: var(--text-tertiary);">验证明亮模式显示效果</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="toggleTestTheme()" class="p-2 rounded-lg transition-colors" style="background-color: var(--bg-tertiary); color: var(--text-primary);">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="flex-1 max-w-7xl mx-auto w-full px-4 py-8" style="background-color: var(--bg-secondary);">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- 左侧：测试区域 -->
                <div class="lg:col-span-2 space-y-4">
                    <!-- 测试消息区域 -->
                    <div class="rounded-2xl p-6 min-h-[400px] space-y-4" style="background-color: var(--bg-primary); border: 1px solid var(--border-primary);">
                        
                        <!-- AI消息测试 -->
                        <div class="ai-message">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <span class="text-white text-sm font-bold">AI</span>
                                </div>
                                <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                                    <h3 class="font-semibold mb-2" style="color: var(--text-primary);">明亮模式测试消息</h3>
                                    <p class="text-sm mb-3" style="color: var(--text-secondary);">这是一条AI消息，用于测试明亮模式下的文字可读性和对比度。</p>
                                    <div class="text-xs" style="color: var(--text-tertiary);">测试各种文字颜色层级的显示效果</div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户消息测试 -->
                        <div class="user-message">
                            <div class="flex items-start space-x-3 justify-end">
                                <div class="flex-1 max-w-md rounded-2xl p-4" style="background: var(--gradient-primary);">
                                    <p class="text-sm text-white">这是用户消息测试，验证渐变背景在明亮模式下的显示效果。</p>
                                </div>
                                <div class="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <span class="text-white text-sm font-bold">U</span>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片测试 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="card rounded-lg p-4" style="background-color: var(--bg-primary); border: 1px solid var(--border-primary);">
                                <h4 class="font-medium mb-2" style="color: var(--text-primary);">测试卡片 1</h4>
                                <p class="text-sm" style="color: var(--text-secondary);">卡片内容测试</p>
                                <div class="mt-3">
                                    <span class="badge primary">主要标签</span>
                                </div>
                            </div>
                            <div class="card rounded-lg p-4" style="background-color: var(--bg-primary); border: 1px solid var(--border-primary);">
                                <h4 class="font-medium mb-2" style="color: var(--text-primary);">测试卡片 2</h4>
                                <p class="text-sm" style="color: var(--text-secondary);">另一个卡片内容</p>
                                <div class="mt-3">
                                    <span class="badge success">成功标签</span>
                                </div>
                            </div>
                        </div>

                        <!-- 按钮测试 -->
                        <div class="flex flex-wrap gap-3">
                            <button class="px-4 py-2 rounded-lg transition-colors" style="background-color: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-primary);">
                                普通按钮
                            </button>
                            <button class="btn-primary px-4 py-2 rounded-lg">
                                主要按钮
                            </button>
                            <button class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                                成功按钮
                            </button>
                            <button class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                危险按钮
                            </button>
                        </div>

                    </div>

                    <!-- 输入框测试 -->
                    <div class="rounded-lg p-4" style="background-color: var(--bg-primary); border: 1px solid var(--border-primary);">
                        <h3 class="font-medium mb-3" style="color: var(--text-primary);">输入框测试</h3>
                        <div class="space-y-3">
                            <input type="text" placeholder="测试输入框..." class="w-full rounded-lg px-3 py-2" style="background-color: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-primary);">
                            <textarea placeholder="测试文本域..." rows="3" class="w-full rounded-lg px-3 py-2" style="background-color: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-primary);"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 右侧：信息面板测试 -->
                <div class="space-y-4">
                    <!-- 账户信息测试 -->
                    <div class="rounded-2xl p-6 transition-all" style="background-color: var(--bg-primary); border: 1px solid var(--border-primary);">
                        <h3 class="text-lg font-semibold mb-4 flex items-center" style="color: var(--text-primary);">
                            <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            测试面板
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm" style="color: var(--text-secondary);">当前卡号</span>
                                <span class="text-sm font-medium" style="color: var(--text-primary);">**** 8899</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm" style="color: var(--text-secondary);">卡片类型</span>
                                <span class="text-sm font-medium text-green-400">储蓄卡</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm" style="color: var(--text-secondary);">账户余额</span>
                                <span class="text-lg font-bold text-blue-400">¥12,580.50</span>
                            </div>
                        </div>
                        
                        <div class="mt-4 pt-4" style="border-top: 1px solid var(--border-primary);">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm" style="color: var(--text-tertiary);">账户活跃度</span>
                                <span class="text-sm text-green-400">活跃</span>
                            </div>
                            <div class="w-full rounded-full h-2" style="background-color: var(--bg-tertiary);">
                                <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息测试 -->
                    <div class="rounded-2xl p-4" style="background-color: var(--bg-primary); border: 1px solid var(--border-primary);">
                        <h4 class="font-semibold text-sm mb-3" style="color: var(--text-primary);">测试统计</h4>
                        <div class="grid grid-cols-2 gap-3 text-center">
                            <div class="rounded-lg p-3" style="background-color: var(--bg-secondary);">
                                <div class="text-lg font-bold text-blue-400">100%</div>
                                <div class="text-xs" style="color: var(--text-tertiary);">明亮模式</div>
                            </div>
                            <div class="rounded-lg p-3" style="background-color: var(--bg-secondary);">
                                <div class="text-lg font-bold text-green-400">优秀</div>
                                <div class="text-xs" style="color: var(--text-tertiary);">对比度</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部输入区域测试 -->
        <div class="backdrop-blur-xl sticky bottom-0" style="border-top: 1px solid var(--border-primary); background-color: var(--bg-primary);">
            <div class="max-w-7xl mx-auto px-4 py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-1 relative">
                        <input type="text" placeholder="测试输入框在明亮模式下的显示效果..."
                               class="w-full rounded-xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all" style="background-color: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-primary);">
                    </div>
                    <button class="btn-primary px-6 py-3 rounded-xl">
                        发送
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTestTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            html.setAttribute('data-theme', newTheme);
            
            // 更新按钮图标
            const button = document.querySelector('button[onclick="toggleTestTheme()"]');
            const icon = button.querySelector('svg path');
            if (newTheme === 'light') {
                icon.setAttribute('d', 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z');
            } else {
                icon.setAttribute('d', 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z');
            }
        }
    </script>
</body>
</html>

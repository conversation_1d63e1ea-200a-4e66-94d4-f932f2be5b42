# 银行AI智能柜员机演示系统 - 代码架构重构PRD

## 一、项目背景与目标

### 1.1 重构背景
当前银行AI智能柜员机演示系统存在以下问题：
- **单体文件过大**：`app.js` 文件超过8600行，难以维护和扩展
- **功能耦合严重**：业务逻辑、UI交互、状态管理混杂在一起
- **演示效果受限**：代码复杂度影响新功能的快速添加和演示调整
- **团队协作困难**：单文件开发容易产生冲突，影响演示迭代速度

### 1.2 重构目标
**核心目标**：构建清晰、可维护的演示系统架构，提升演示效果和开发效率

**具体目标**：
- 🎯 **演示友好**：支持快速添加新的演示场景和业务流程
- 🔧 **开发高效**：模块化开发，支持并行开发不同演示功能
- 📱 **易于扩展**：新增银行业务演示场景时代码改动最小
- 🎨 **演示灵活**：支持不同演示模式（完整流程/单功能展示/对比演示）
- 📊 **效果可控**：便于调整演示参数和效果，适应不同演示需求

### 1.3 演示系统特点
- **非生产环境**：专注演示效果，不需要考虑生产级安全和性能
- **快速迭代**：支持演示内容的快速调整和新场景添加
- **可视化优先**：重点突出AI思维过程和用户体验展示
- **模拟数据**：使用精心设计的模拟数据展示最佳效果

## 二、架构设计原则

### 2.1 演示导向原则
- **场景驱动**：以演示场景为核心组织代码结构
- **效果优先**：优先考虑演示效果，简化不必要的复杂性
- **快速响应**：支持演示需求的快速变更和调整

### 2.2 技术原则
- **轻量化**：保持技术栈简单，避免过度工程化
- **模块化**：功能模块独立，便于单独演示和组合演示
- **可配置**：演示参数可配置，支持不同演示场景

### 2.3 开发原则
- **渐进重构**：分阶段重构，确保演示系统持续可用
- **向后兼容**：重构过程中保持现有演示功能正常
- **文档同步**：重构的同时更新演示说明文档

## 三、目标架构设计

### 3.1 整体架构图
```
banking-ai-agent/
├── index.html                 # 主演示页面
├── config/                    # 配置文件
│   ├── demo-config.js         # 演示配置
│   ├── business-config.js     # 业务场景配置
│   └── ui-config.js           # UI主题配置
├── core/                      # 核心系统
│   ├── app.js                 # 应用主控制器
│   ├── state-manager.js       # 状态管理
│   ├── event-bus.js           # 事件总线
│   └── demo-controller.js     # 演示控制器
├── modules/                   # 功能模块
│   ├── ai-engine/             # AI引擎模块
│   ├── banking-services/      # 银行业务模块
│   ├── ui-components/         # UI组件模块
│   ├── user-interaction/      # 用户交互模块
│   └── demo-scenarios/        # 演示场景模块
├── utils/                     # 工具函数
│   ├── animation-utils.js     # 动画工具
│   ├── mock-data.js           # 模拟数据
│   └── demo-utils.js          # 演示工具
├── assets/                    # 静态资源
└── styles/                    # 样式文件
```

### 3.2 核心模块设计

#### 3.2.1 演示控制器 (demo-controller.js)
**职责**：管理演示流程、场景切换、演示参数
```javascript
class DemoController {
    // 演示场景管理
    loadScenario(scenarioId)
    switchScenario(fromId, toId)
    resetScenario()
    
    // 演示参数控制
    setDemoSpeed(speed)
    setDemoMode(mode) // 'auto' | 'manual' | 'guided'
    
    // 演示状态管理
    pauseDemo()
    resumeDemo()
    skipToStep(stepId)
}
```

#### 3.2.2 AI引擎模块 (ai-engine/)
**职责**：AI思维链展示、智能推荐、对话处理
```
ai-engine/
├── thinking-chain.js          # AI思维链可视化
├── recommendation-engine.js   # 智能推荐引擎
├── conversation-handler.js    # 对话处理
└── intent-analyzer.js         # 意图分析
```

#### 3.2.3 银行业务模块 (banking-services/)
**职责**：各类银行业务的演示逻辑
```
banking-services/
├── yinxintong/               # 银信通业务
│   ├── signup-flow.js        # 签约流程
│   ├── modify-flow.js        # 修改流程
│   └── cancel-flow.js        # 解约流程
├── transfer/                 # 转账业务
├── account/                  # 账户业务
└── security/                 # 安全认证
```

#### 3.2.4 演示场景模块 (demo-scenarios/)
**职责**：预定义的演示场景和流程
```
demo-scenarios/
├── complete-journey.js       # 完整用户旅程
├── ai-capabilities.js        # AI能力展示
├── business-comparison.js    # 业务对比演示
└── user-experience.js        # 用户体验演示
```

### 3.3 数据流设计

#### 3.3.1 演示数据流
```
用户操作 → 演示控制器 → 场景模块 → 业务模块 → UI更新
    ↓
事件总线 ← 状态管理器 ← AI引擎 ← 用户交互模块
```

#### 3.3.2 状态管理
```javascript
// 演示状态结构
const demoState = {
    currentScenario: 'yinxintong-signup',
    demoMode: 'guided',
    currentStep: 3,
    userProfile: { /* 模拟用户数据 */ },
    businessContext: { /* 业务上下文 */ },
    uiState: { /* UI状态 */ }
}
```

## 四、重构实施计划

### 4.1 第一阶段：核心框架搭建（1周）
**目标**：建立基础架构，确保演示系统可运行

**任务清单**：
- [ ] 创建目录结构和基础文件
- [ ] 实现演示控制器基础功能
- [ ] 建立事件总线和状态管理
- [ ] 迁移核心AI思维链功能
- [ ] 确保银信通基础演示可运行

**验收标准**：
- 新架构下银信通签约演示正常运行
- 演示控制器可以管理基本演示流程
- 代码结构清晰，模块职责明确

### 4.2 第二阶段：业务模块迁移（1周）
**目标**：迁移所有银行业务演示功能

**任务清单**：
- [ ] 迁移银信通完整业务流程
- [ ] 迁移转账汇款演示功能
- [ ] 迁移用户交互和UI组件
- [ ] 建立模拟数据管理
- [ ] 优化演示场景配置

**验收标准**：
- 所有现有演示功能正常工作
- 新增演示场景配置灵活
- 演示效果不低于重构前

### 4.3 第三阶段：演示增强（1周）
**目标**：增强演示效果和可配置性

**任务清单**：
- [ ] 实现演示场景快速切换
- [ ] 添加演示参数实时调整
- [ ] 优化AI思维链展示效果
- [ ] 增加演示模式选择
- [ ] 完善演示文档和说明

**验收标准**：
- 支持多种演示模式
- 演示参数可实时调整
- 演示效果显著提升

## 五、技术实现细节

### 5.1 模块加载机制
```javascript
// 使用ES6模块化，支持按需加载
class ModuleLoader {
    async loadModule(moduleName) {
        const module = await import(`./modules/${moduleName}/index.js`);
        return module.default;
    }
    
    async loadScenario(scenarioName) {
        const scenario = await import(`./demo-scenarios/${scenarioName}.js`);
        return scenario.default;
    }
}
```

### 5.2 配置管理
```javascript
// demo-config.js - 演示配置
export const demoConfig = {
    defaultScenario: 'yinxintong-signup',
    animationSpeed: 'normal', // 'slow' | 'normal' | 'fast'
    autoAdvance: true,
    showThinkingChain: true,
    mockDataLevel: 'rich' // 'basic' | 'rich' | 'realistic'
};
```

### 5.3 事件系统
```javascript
// 演示事件定义
const DemoEvents = {
    SCENARIO_CHANGED: 'scenario:changed',
    STEP_COMPLETED: 'step:completed',
    USER_INTERACTION: 'user:interaction',
    AI_THINKING_START: 'ai:thinking:start',
    AI_THINKING_END: 'ai:thinking:end'
};
```

## 六、演示场景设计

### 6.1 预定义演示场景

#### 6.1.1 完整用户旅程演示
- **场景名称**：complete-user-journey
- **演示时长**：5-8分钟
- **核心展示**：从用户进入到业务完成的完整流程
- **关键亮点**：AI智能感知、个性化推荐、流程可视化

#### 6.1.2 AI能力专项演示
- **场景名称**：ai-capabilities-showcase
- **演示时长**：3-5分钟
- **核心展示**：AI思维链、智能推荐、对话理解
- **关键亮点**：AI透明度、决策过程、学习能力

#### 6.1.3 用户体验对比演示
- **场景名称**：ux-comparison
- **演示时长**：5-7分钟
- **核心展示**：传统流程 vs AI优化流程
- **关键亮点**：效率提升、体验改善、错误减少

### 6.2 演示参数配置
```javascript
// 场景配置示例
const scenarioConfig = {
    'yinxintong-signup': {
        steps: [
            { id: 'greeting', duration: 2000, autoAdvance: true },
            { id: 'need-analysis', duration: 3000, autoAdvance: false },
            { id: 'solution-generation', duration: 4000, autoAdvance: true },
            // ...
        ],
        mockData: {
            userProfile: { /* 用户数据 */ },
            recommendations: [ /* 推荐方案 */ ]
        },
        uiSettings: {
            showConfidence: true,
            highlightAIThinking: true,
            enableInteraction: true
        }
    }
};
```

## 七、质量保证

### 7.1 演示质量标准
- **流畅性**：演示过程无卡顿，动画流畅
- **一致性**：不同场景下UI和交互保持一致
- **可靠性**：演示过程不出现错误或异常
- **灵活性**：支持演示参数的实时调整

### 7.2 测试策略
- **功能测试**：确保所有演示功能正常
- **兼容性测试**：主流浏览器兼容性验证
- **性能测试**：演示流畅度和响应速度
- **用户测试**：演示效果和理解度验证

### 7.3 文档要求
- **架构文档**：详细的模块设计和接口说明
- **演示指南**：各种演示场景的使用说明
- **配置手册**：演示参数配置和调整指南
- **故障排除**：常见问题和解决方案

## 八、风险控制

### 8.1 重构风险
- **功能回退风险**：通过分阶段重构和充分测试控制
- **演示效果风险**：保持演示效果不低于重构前
- **时间风险**：合理安排重构进度，确保演示需求

### 8.2 应急预案
- **回滚机制**：保留重构前版本，支持快速回滚
- **并行开发**：重构期间保持原版本可用
- **渐进发布**：分模块验证和发布

## 九、成功标准

### 9.1 技术指标
- 代码行数：单文件不超过500行
- 模块耦合度：模块间依赖清晰可控
- 加载性能：页面加载时间不超过2秒
- 演示流畅度：动画帧率保持60fps

### 9.2 业务指标
- 演示场景：支持至少5种不同演示场景
- 配置灵活性：演示参数可实时调整
- 开发效率：新增演示场景开发时间减少50%
- 维护成本：代码维护和更新效率提升70%

### 9.3 用户体验指标
- 演示理解度：观众对AI能力理解度提升
- 演示吸引力：演示过程观众参与度提升
- 技术印象：对AI技术先进性认知提升

---

**项目负责人**：开发团队  
**预计完成时间**：3周  
**下一步行动**：开始第一阶段核心框架搭建

<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行AI智能柜员机演示系统 - 重构版</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    
    <!-- 配置Tailwind主题 -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#3B82F6',
                        'secondary': '#1F2937',
                        'accent': '#10B981'
                    }
                }
            }
        }
    </script>
    
    <style>
        /* 自定义CSS变量 */
        :root {
            --bg-primary: #0F172A;
            --bg-secondary: #1E293B;
            --text-primary: #F1F5F9;
            --text-secondary: #94A3B8;
            --border-primary: #334155;
            --accent-blue: #3B82F6;
            --accent-green: #10B981;
        }
        
        [data-theme="light"] {
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --text-primary: #1E293B;
            --text-secondary: #64748B;
            --border-primary: #E2E8F0;
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .chat-container {
            background-color: var(--bg-secondary);
            border-color: var(--border-primary);
        }
        
        /* 滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 主容器 -->
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <div class="w-80 bg-gray-900 border-r border-gray-700 flex flex-col">
            <!-- 头部 -->
            <div class="p-4 border-b border-gray-700">
                <h1 class="text-lg font-bold text-white">银行AI智能助手</h1>
                <p class="text-sm text-gray-400">演示系统 v2.0</p>
            </div>
            
            <!-- 演示控制面板 -->
            <div class="p-4 border-b border-gray-700">
                <h3 class="text-sm font-medium text-gray-300 mb-3">演示控制</h3>
                <div class="space-y-2">
                    <button id="start-demo" class="w-full px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm text-white">
                        开始演示
                    </button>
                    <button id="pause-demo" class="w-full px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded text-sm text-white" disabled>
                        暂停演示
                    </button>
                    <button id="reset-demo" class="w-full px-3 py-2 bg-red-600 hover:bg-red-700 rounded text-sm text-white">
                        重置演示
                    </button>
                </div>
            </div>
            
            <!-- 场景选择 -->
            <div class="p-4 border-b border-gray-700">
                <h3 class="text-sm font-medium text-gray-300 mb-3">演示场景</h3>
                <select id="scenario-select" class="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-sm text-white">
                    <option value="yinxintong-signup">银信通签约流程</option>
                    <option value="ai-capabilities">AI能力展示</option>
                    <option value="ux-comparison">用户体验对比</option>
                </select>
            </div>
            
            <!-- 演示设置 -->
            <div class="p-4 border-b border-gray-700">
                <h3 class="text-sm font-medium text-gray-300 mb-3">演示设置</h3>
                <div class="space-y-3">
                    <div>
                        <label class="block text-xs text-gray-400 mb-1">演示模式</label>
                        <select id="demo-mode" class="w-full px-2 py-1 bg-gray-800 border border-gray-600 rounded text-xs text-white">
                            <option value="guided">引导模式</option>
                            <option value="auto">自动模式</option>
                            <option value="interactive">交互模式</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-xs text-gray-400 mb-1">动画速度</label>
                        <select id="animation-speed" class="w-full px-2 py-1 bg-gray-800 border border-gray-600 rounded text-xs text-white">
                            <option value="slow">慢速</option>
                            <option value="normal" selected>正常</option>
                            <option value="fast">快速</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="show-thinking" checked class="rounded">
                        <label for="show-thinking" class="text-xs text-gray-400">显示AI思维链</label>
                    </div>
                </div>
            </div>
            
            <!-- 演示进度 -->
            <div class="p-4 flex-1">
                <h3 class="text-sm font-medium text-gray-300 mb-3">演示进度</h3>
                <div class="space-y-2">
                    <div class="flex justify-between text-xs text-gray-400">
                        <span id="step-indicator">步骤 0/0</span>
                        <span id="progress-percentage">0%</span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="current-step-name" class="text-xs text-gray-500">等待开始...</div>
                </div>
            </div>
            
            <!-- 底部工具 -->
            <div class="p-4 border-t border-gray-700">
                <div class="flex space-x-2">
                    <button id="theme-toggle" class="flex-1 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm text-white">
                        🌙 深色
                    </button>
                    <button id="fullscreen-toggle" class="flex-1 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm text-white">
                        📺 全屏
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部状态栏 -->
            <div class="h-12 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-4">
                <div class="flex items-center space-x-4">
                    <div id="demo-status" class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-gray-500 rounded-full"></div>
                        <span class="text-sm text-gray-400">演示未开始</span>
                    </div>
                    <div id="ai-status" class="flex items-center space-x-2">
                        <span class="text-sm text-gray-400">AI助手就绪</span>
                    </div>
                </div>
                <div class="flex items-center space-x-2 text-xs text-gray-500">
                    <span>演示系统</span>
                    <span>|</span>
                    <span id="current-time"></span>
                </div>
            </div>
            
            <!-- 聊天区域 -->
            <div class="flex-1 flex flex-col">
                <!-- 消息容器 -->
                <div id="chat-messages" class="flex-1 overflow-y-auto p-4 space-y-4 custom-scrollbar">
                    <!-- 消息将在这里动态添加 -->
                </div>
                
                <!-- 输入区域 -->
                <div class="border-t border-gray-700 p-4">
                    <div class="flex space-x-3">
                        <input 
                            type="text" 
                            id="user-input" 
                            placeholder="请输入您的需求，例如：我要开通银信通..." 
                            class="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                        >
                        <button 
                            id="send-button" 
                            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors"
                        >
                            发送
                        </button>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        💡 提示：按 Enter 发送消息，按 Space 暂停/继续演示
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载提示 -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 text-center">
            <div class="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <div class="text-white">正在初始化演示系统...</div>
        </div>
    </div>
    
    <!-- 错误提示模态框 -->
    <div id="error-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-gray-800 rounded-lg p-6 max-w-md">
            <div class="flex items-center space-x-3 mb-4">
                <div class="text-red-500 text-2xl">⚠️</div>
                <h3 class="text-lg font-medium text-white">系统错误</h3>
            </div>
            <p id="error-message" class="text-gray-300 mb-4"></p>
            <div class="flex justify-end space-x-3">
                <button id="error-close" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-white">
                    关闭
                </button>
                <button id="error-retry" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white">
                    重试
                </button>
            </div>
        </div>
    </div>
    
    <!-- 脚本加载 -->
    <script type="module">
        // 导入核心模块
        import { initializeApp } from './core/app.js';
        import { eventBus, DemoEvents } from './core/event-bus.js';
        import { demoController } from './core/demo-controller.js';
        import { yinxintongSignupFlow } from './modules/banking-services/yinxintong/signup-flow.js';
        
        // 全局变量
        let app = null;
        
        // 初始化应用
        async function init() {
            try {
                console.log('Initializing Banking AI Demo System...');
                
                // 初始化应用
                app = await initializeApp({
                    demo: {
                        autoStart: false,
                        showDemoControls: true
                    }
                });
                
                // 设置UI事件监听
                setupUIEventListeners();
                
                // 设置演示事件监听
                setupDemoEventListeners();
                
                // 隐藏加载提示
                document.getElementById('loading-overlay').style.display = 'none';
                
                // 显示欢迎消息
                showWelcomeMessage();
                
                console.log('Banking AI Demo System initialized successfully');
                
            } catch (error) {
                console.error('Failed to initialize app:', error);
                showError('系统初始化失败', error.message);
            }
        }
        
        // 设置UI事件监听
        function setupUIEventListeners() {
            // 演示控制按钮
            document.getElementById('start-demo').addEventListener('click', () => {
                const scenarioId = document.getElementById('scenario-select').value;
                const mode = document.getElementById('demo-mode').value;
                const speed = document.getElementById('animation-speed').value;
                
                app.startDemo(scenarioId, { mode, speed });
            });
            
            document.getElementById('pause-demo').addEventListener('click', () => {
                if (demoController.isPaused()) {
                    demoController.resumeDemo();
                } else {
                    demoController.pauseDemo();
                }
            });
            
            document.getElementById('reset-demo').addEventListener('click', () => {
                app.resetDemo();
            });
            
            // 场景选择
            document.getElementById('scenario-select').addEventListener('change', (e) => {
                app.switchScenario(e.target.value);
            });
            
            // 设置变更
            document.getElementById('demo-mode').addEventListener('change', (e) => {
                demoController.setDemoMode(e.target.value);
            });
            
            document.getElementById('animation-speed').addEventListener('change', (e) => {
                demoController.setDemoSpeed(e.target.value);
            });
            
            // 主题切换
            document.getElementById('theme-toggle').addEventListener('click', () => {
                app.toggleTheme();
            });
            
            // 全屏切换
            document.getElementById('fullscreen-toggle').addEventListener('click', () => {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            });
            
            // 错误模态框
            document.getElementById('error-close').addEventListener('click', () => {
                document.getElementById('error-modal').classList.add('hidden');
            });
            
            document.getElementById('error-retry').addEventListener('click', () => {
                document.getElementById('error-modal').classList.add('hidden');
                location.reload();
            });
        }
        
        // 设置演示事件监听
        function setupDemoEventListeners() {
            // 演示状态变更
            eventBus.on(DemoEvents.DEMO_STARTED, () => {
                updateDemoStatus('running', '演示进行中');
                document.getElementById('start-demo').disabled = true;
                document.getElementById('pause-demo').disabled = false;
            });
            
            eventBus.on(DemoEvents.DEMO_PAUSED, () => {
                updateDemoStatus('paused', '演示已暂停');
                document.getElementById('pause-demo').textContent = '继续演示';
            });
            
            eventBus.on(DemoEvents.DEMO_RESUMED, () => {
                updateDemoStatus('running', '演示进行中');
                document.getElementById('pause-demo').textContent = '暂停演示';
            });
            
            eventBus.on(DemoEvents.DEMO_STOPPED, () => {
                updateDemoStatus('stopped', '演示已停止');
                document.getElementById('start-demo').disabled = false;
                document.getElementById('pause-demo').disabled = true;
                document.getElementById('pause-demo').textContent = '暂停演示';
            });
            
            // 步骤变更
            eventBus.on(DemoEvents.STEP_STARTED, (event) => {
                const { stepIndex, step } = event.data;
                const totalSteps = demoController.currentScenario?.config.steps.length || 0;
                const progress = Math.round((stepIndex / totalSteps) * 100);
                
                document.getElementById('step-indicator').textContent = `步骤 ${stepIndex + 1}/${totalSteps}`;
                document.getElementById('progress-percentage').textContent = `${progress}%`;
                document.getElementById('progress-bar').style.width = `${progress}%`;
                document.getElementById('current-step-name').textContent = step.name || step.id;
            });
            
            // AI思维状态
            eventBus.on(DemoEvents.AI_THINKING_START, () => {
                updateAIStatus('AI正在思考...');
            });
            
            eventBus.on(DemoEvents.AI_THINKING_END, () => {
                updateAIStatus('AI助手就绪');
            });
            
            // 主题变更
            eventBus.on(DemoEvents.UI_THEME_CHANGED, (event) => {
                const theme = event.data.theme;
                document.getElementById('theme-toggle').textContent = theme === 'dark' ? '🌙 深色' : '☀️ 浅色';
            });
        }
        
        // 更新演示状态
        function updateDemoStatus(status, text) {
            const statusElement = document.getElementById('demo-status');
            const dot = statusElement.querySelector('.w-2');
            const span = statusElement.querySelector('span');
            
            // 更新状态点颜色
            dot.className = 'w-2 h-2 rounded-full ' + {
                'running': 'bg-green-500',
                'paused': 'bg-yellow-500',
                'stopped': 'bg-gray-500'
            }[status];
            
            span.textContent = text;
        }
        
        // 更新AI状态
        function updateAIStatus(text) {
            document.getElementById('ai-status').querySelector('span').textContent = text;
        }
        
        // 显示欢迎消息
        function showWelcomeMessage() {
            app.addAIMessage(`
                <div class="bg-gradient-to-r from-blue-900/30 to-purple-900/30 border border-blue-600/30 rounded-lg p-4">
                    <div class="flex items-center space-x-2 mb-3">
                        <span class="text-blue-400">🤖</span>
                        <span class="font-semibold text-blue-400">银行AI智能助手 v2.0</span>
                    </div>
                    <p class="text-sm text-gray-300 mb-3">
                        欢迎使用重构版演示系统！我是您的专属银行AI助手，可以帮您办理各种银行业务。
                    </p>
                    <div class="text-xs text-gray-400 space-y-1">
                        <div>💡 您可以点击左侧"开始演示"按钮开始体验</div>
                        <div>🎯 或者直接输入"我要开通银信通"开始对话</div>
                        <div>⚙️ 可以在左侧面板调整演示模式和速度</div>
                    </div>
                </div>
            `);
        }
        
        // 显示错误
        function showError(title, message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-modal').classList.remove('hidden');
        }
        
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { hour12: false });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // 启动时间更新
        setInterval(updateTime, 1000);
        updateTime();
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
        
        // 暴露到全局（用于调试）
        window.demoApp = {
            app,
            eventBus,
            demoController,
            yinxintongSignupFlow
        };
    </script>
</body>
</html>

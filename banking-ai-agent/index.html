<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能银行助手 - AI驱动的金融服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">

    <!-- 无障碍样式 -->
    <style>
        /* 屏幕阅读器专用内容 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* 获得焦点时显示 */
        .sr-only:focus,
        .focus\:not-sr-only:focus {
            position: static;
            width: auto;
            height: auto;
            padding: 0.5rem;
            margin: 0;
            overflow: visible;
            clip: auto;
            white-space: normal;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            border: 2px solid var(--accent-primary);
            border-radius: 0.375rem;
            z-index: 50;
        }

        /* 跳转链接样式 */
        .skip-links {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 100;
            padding: 1rem;
        }

        .skip-link {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin-right: 0.5rem;
            background-color: var(--accent-primary);
            color: white;
            text-decoration: none;
            border-radius: 0.375rem;
            font-weight: 500;
        }

        .skip-link:hover,
        .skip-link:focus {
            background-color: var(--accent-secondary);
            outline: 2px solid white;
            outline-offset: 2px;
        }

        /* 高对比度模式 */
        .high-contrast {
            --bg-primary: #000000;
            --bg-secondary: #1a1a1a;
            --bg-tertiary: #2a2a2a;
            --text-primary: #ffffff;
            --text-secondary: #e0e0e0;
            --border-primary: #ffffff;
            --accent-primary: #00ff00;
            --accent-secondary: #ffff00;
        }

        .high-contrast button,
        .high-contrast input {
            border: 2px solid var(--border-primary) !important;
        }

        /* 减少动画模式 */
        .reduced-motion *,
        .reduced-motion *::before,
        .reduced-motion *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
        }

        /* 焦点指示器增强 */
        button:focus,
        input:focus,
        [tabindex]:focus {
            outline: 2px solid var(--accent-primary);
            outline-offset: 2px;
        }

        /* 大字体模式支持 */
        @media (min-resolution: 2dppx) {
            body {
                font-size: 1.1em;
            }
        }

        /* 触摸设备优化 */
        @media (pointer: coarse) {
            button,
            .service-button {
                min-height: 44px;
                min-width: 44px;
            }
        }
    </style>
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary); color: var(--text-primary);">
    <!-- AI Agent 界面容器 -->
    <div id="ai-agent-container" class="min-h-screen flex flex-col">
        
        <!-- 顶部导航栏 -->
        <nav class="backdrop-blur-xl sticky top-0 z-50" style="background-color: var(--bg-primary); border-bottom: 1px solid var(--border-primary);">
            <div class="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-sm">AI</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-semibold" style="color: var(--text-primary);">智能银行助手</h1>
                        <p class="text-xs" style="color: var(--text-tertiary);">AI驱动的智能金融服务</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="theme-toggle" class="p-2 rounded-lg transition-colors" style="background-color: var(--bg-tertiary); color: var(--text-primary);" onmouseover="this.style.backgroundColor='var(--bg-quaternary)'" onmouseout="this.style.backgroundColor='var(--bg-tertiary)'">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                    <!-- 核心支柱：逃生通道 - 无缝人工介入 -->
                    <button onclick="aiAgent.contactHumanAgent()"
                            class="px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 rounded-lg transition-all flex items-center space-x-2 shadow-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>联系专家</span>
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    </button>

                    <!-- 核心支柱：普惠关怀 - 无障碍设计 -->
                    <button onclick="aiAgent.toggleAccessibilityMode()"
                            class="p-2 rounded-lg transition-colors" style="background-color: var(--bg-tertiary); color: var(--text-primary);" onmouseover="this.style.backgroundColor='var(--bg-quaternary)'" onmouseout="this.style.backgroundColor='var(--bg-tertiary)'"
                            title="无障碍模式">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </button>

                    <!-- 演示控制 -->
                    <button onclick="toggleDemoControl()"
                            class="p-2 rounded-lg transition-colors" style="background-color: var(--bg-tertiary); color: var(--text-primary);" onmouseover="this.style.backgroundColor='var(--bg-quaternary)'" onmouseout="this.style.backgroundColor='var(--bg-tertiary)'"
                            title="演示控制">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a1 1 0 001 1h4M9 10V9a1 1 0 011-1h4a1 1 0 011 1v1M9 10H8a1 1 0 00-1 1v3a1 1 0 001 1h1m0-4h4.586M15 13a1 1 0 001-1V9a1 1 0 00-1-1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 演示控制面板 -->
        <div id="demo-control-panel" class="fixed top-20 right-4 bg-gray-900/95 backdrop-blur-sm rounded-lg p-4 border border-gray-700 shadow-xl z-40 hidden">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-sm font-semibold text-white">演示控制</h3>
                <button onclick="toggleDemoControl()" class="text-gray-400 hover:text-white">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-3">
                <div class="flex items-center space-x-2">
                    <button onclick="startDemo()" class="flex-1 px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-xs text-white transition-colors">
                        开始演示
                    </button>
                    <button onclick="stopDemo()" class="flex-1 px-3 py-2 bg-red-600 hover:bg-red-700 rounded text-xs text-white transition-colors">
                        停止演示
                    </button>
                </div>

                <div class="flex items-center space-x-2">
                    <button onclick="pauseDemo()" class="flex-1 px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded text-xs text-white transition-colors">
                        暂停
                    </button>
                    <button onclick="resumeDemo()" class="flex-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-xs text-white transition-colors">
                        继续
                    </button>
                </div>

                <div class="border-t border-gray-600 pt-3">
                    <label class="block text-xs text-gray-300 mb-2">演示场景</label>
                    <select id="demo-scenario" onchange="changeScenario(this.value)" class="w-full px-2 py-1 bg-gray-800 text-white rounded text-xs border border-gray-600">
                        <option value="banking_services">银行服务演示</option>
                        <option value="ai_features">AI功能演示</option>
                        <option value="accessibility">无障碍功能演示</option>
                        <option value="full_demo">完整功能演示</option>
                    </select>
                </div>

                <div class="border-t border-gray-600 pt-3">
                    <label class="block text-xs text-gray-300 mb-2">演示速度</label>
                    <input type="range" id="demo-speed" min="0.5" max="3" step="0.5" value="1" onchange="changeDemoSpeed(this.value)"
                           class="w-full">
                    <div class="flex justify-between text-xs text-gray-400 mt-1">
                        <span>慢</span>
                        <span>正常</span>
                        <span>快</span>
                    </div>
                </div>

                <div id="demo-status" class="text-xs text-gray-400 text-center py-2">
                    演示未启动
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <main class="flex-1 max-w-7xl mx-auto w-full px-4 py-8" style="background-color: var(--bg-secondary);">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- 左侧：对话区域 -->
                <div class="lg:col-span-2 space-y-4">
                    <!-- 快速服务入口 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 animate-fade-in">
                        <h3 class="text-sm font-semibold mb-3 flex items-center" style="color: var(--text-primary);">
                            <svg class="w-4 h-4 mr-2 text-blue-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            快速服务
                        </h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3" role="group" aria-label="快速服务选项">
                            <button onclick="aiAgent.handleSmsServiceRequest('sms_notification', '我想开通银信通')"
                                    class="service-button p-3 bg-blue-600/20 rounded-lg hover:bg-blue-600/30 transition-all duration-300 text-center group hover:scale-105 hover:shadow-lg focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                    aria-label="银信通短信提醒服务" tabindex="0">
                                <div class="text-2xl mb-1 group-hover:animate-bounce" aria-hidden="true">📱</div>
                                <div class="text-xs font-medium text-blue-400 group-hover:text-blue-300">银信通</div>
                                <div class="text-xs text-gray-400 group-hover:text-gray-300">短信提醒</div>
                            </button>
                            <button onclick="aiAgent.handleTransferRequest('我想转账')"
                                    class="service-button p-3 bg-green-600/20 rounded-lg hover:bg-green-600/30 transition-all duration-300 text-center group hover:scale-105 hover:shadow-lg focus:ring-2 focus:ring-green-400 focus:outline-none"
                                    aria-label="转账汇款服务" tabindex="0">
                                <div class="text-2xl mb-1 group-hover:animate-bounce" aria-hidden="true">💸</div>
                                <div class="text-xs font-medium text-green-400 group-hover:text-green-300">转账汇款</div>
                                <div class="text-xs text-gray-400 group-hover:text-gray-300">快速转账</div>
                            </button>
                            <button onclick="aiAgent.handleAccountManagementRequest('账户查询')"
                                    class="service-button p-3 bg-purple-600/20 rounded-lg hover:bg-purple-600/30 transition-all duration-300 text-center group hover:scale-105 hover:shadow-lg focus:ring-2 focus:ring-purple-400 focus:outline-none"
                                    aria-label="账户查询服务" tabindex="0">
                                <div class="text-2xl mb-1 group-hover:animate-bounce" aria-hidden="true">💰</div>
                                <div class="text-xs font-medium text-purple-400 group-hover:text-purple-300">账户查询</div>
                                <div class="text-xs text-gray-400 group-hover:text-gray-300">余额明细</div>
                            </button>
                            <button onclick="aiAgent.showGeneralHelpMessage()"
                                    class="service-button p-3 bg-orange-600/20 rounded-lg hover:bg-orange-600/30 transition-all duration-300 text-center group hover:scale-105 hover:shadow-lg focus:ring-2 focus:ring-orange-400 focus:outline-none"
                                    aria-label="AI智能助手服务" tabindex="0">
                                <div class="text-2xl mb-1 group-hover:animate-bounce" aria-hidden="true">🤖</div>
                                <div class="text-xs font-medium text-orange-400 group-hover:text-orange-300">AI助手</div>
                                <div class="text-xs text-gray-400 group-hover:text-gray-300">智能服务</div>
                            </button>
                        </div>
                    </div>

                    <!-- 对话消息区域容器 -->
                    <div class="chat-container rounded-2xl relative" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <!-- 对话消息区域 -->
                        <div id="chat-messages" class="chat-messages-area">
                            <!-- 智能欢迎消息 -->
                            <div class="ai-message" id="welcome-message">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-sm font-bold">AI</span>
                                    </div>
                                    <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                                        <!-- 动态加载的个性化欢迎内容 -->
                                        <div id="personalized-greeting" style="color: var(--text-primary);">
                                            <!-- 将由JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 滚动到底部按钮 -->
                        <button id="scroll-to-bottom" class="scroll-to-bottom-btn hidden" onclick="aiAgent.scrollToBottom()" title="滚动到底部">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                            </svg>
                        </button>
                    </div>



                    <!-- 动态方案展示区 -->
                    <div id="solution-cards" class="hidden space-y-4">
                        <!-- 动态插入方案卡片 -->
                    </div>

                    <!-- 执行进度展示 -->
                    <div id="execution-progress" class="hidden rounded-2xl p-6" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <h3 class="text-lg font-semibold mb-4 flex items-center" style="color: var(--text-primary);">
                            <svg class="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            执行进度
                        </h3>
                        <div id="progress-steps" class="space-y-3">
                            <!-- 动态插入执行步骤 -->
                        </div>
                    </div>
                </div>

                <!-- 右侧：信息面板 -->
                <div class="lg:col-span-1 space-y-4">
                    <!-- 账户信息卡片 -->
                    <div class="bg-gray-900/50 rounded-2xl p-6 border border-gray-800 hover:border-gray-700 transition-all">
                        <h3 class="text-lg font-semibold mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            账户信息
                            <div class="ml-auto">
                                <div class="status-indicator online" title="实时同步"></div>
                            </div>
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">当前卡号</span>
                                <span class="font-mono text-blue-400">**** 8899</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">卡片类型</span>
                                <span class="badge success">工资卡</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">服务状态</span>
                                <span id="service-status" class="badge warning">未开通</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">月均交易</span>
                                <span class="text-blue-400 font-semibold">18笔</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">账户余额</span>
                                <span class="text-green-400 font-semibold">¥12,580.50</span>
                            </div>
                        </div>

                        <!-- 账户活跃度指示器 -->
                        <div class="mt-4 pt-4 border-t border-gray-700">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-400">账户活跃度</span>
                                <span class="text-sm text-green-400">活跃</span>
                            </div>
                            <div class="w-full bg-gray-800 rounded-full h-2">
                                <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full animate-pulse" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- AI 智能分析 -->
                    <div id="ai-insights" class="bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl p-6 border border-blue-800/50 hover:border-blue-700/70 transition-all">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-blue-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold">AI 智能分析</h3>
                            </div>
                            <div class="badge primary">实时分析</div>
                        </div>

                        <div class="space-y-3">
                            <p class="text-sm text-gray-300 leading-relaxed">基于您的账户使用情况和交易模式，我为您分析了当前的金融服务状态，并提供个性化的优化建议。</p>

                            <!-- 智能建议列表 -->
                            <div class="space-y-2">
                                <div class="flex items-start space-x-2 text-xs">
                                    <span class="text-green-400">✓</span>
                                    <span class="text-gray-400">账户活跃度良好，适合开通增值服务</span>
                                </div>
                                <div class="flex items-start space-x-2 text-xs">
                                    <span class="text-blue-400">💡</span>
                                    <span class="text-gray-400">可考虑理财产品提升资金收益</span>
                                </div>
                                <div class="flex items-start space-x-2 text-xs">
                                    <span class="text-purple-400">📊</span>
                                    <span class="text-gray-400">建议开通智能通知服务</span>
                                </div>
                            </div>

                            <!-- 置信度指示器 -->
                            <div class="mt-4 pt-3 border-t border-blue-800/30">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-xs text-gray-400">分析置信度</span>
                                    <span class="text-xs text-blue-400 font-semibold">92%</span>
                                </div>
                                <div class="w-full bg-gray-800 rounded-full h-1.5">
                                    <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full" style="width: 92%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时通知面板 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 hover:border-gray-700 transition-all">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-sm flex items-center">
                                <svg class="w-4 h-4 mr-2 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 7l2.829 2.828A4 4 0 019.071 11H13l-4 8-4-8h3.071a4 4 0 01-2.414-1.172L2.828 7z"></path>
                                </svg>
                                实时通知
                            </h4>
                            <div class="status-indicator online"></div>
                        </div>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                                <span class="text-gray-400">今日通知</span>
                                <span class="text-blue-400 font-semibold">3条</span>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                                <span class="text-gray-400">本月费用</span>
                                <span class="text-green-400 font-semibold">¥2.00</span>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                                <span class="text-gray-400">节省通知</span>
                                <span class="text-purple-400 font-semibold">12条</span>
                            </div>
                        </div>
                    </div>

                    <!-- 安全提示 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 hover:border-green-800/50 transition-all">
                        <div class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <div>
                                <h4 class="font-semibold text-sm">安全保障</h4>
                                <p class="text-xs text-gray-400 mt-1">全程加密传输，支持随时撤销</p>
                                <div class="flex items-center mt-2 space-x-2">
                                    <div class="badge success">SSL加密</div>
                                    <div class="badge primary">实时监控</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速统计 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800">
                        <h4 class="font-semibold text-sm mb-3">服务统计</h4>
                        <div class="grid grid-cols-2 gap-3 text-center">
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="text-lg font-bold text-blue-400">2.3万</div>
                                <div class="text-xs text-gray-400">今日服务人次</div>
                            </div>
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="text-lg font-bold text-green-400">98.5%</div>
                                <div class="text-xs text-gray-400">用户满意度</div>
                            </div>
                        </div>

                        <div class="mt-4 text-center">
                            <button onclick="showSystemMonitoring()" class="text-sm text-green-400 hover:text-green-300 transition-colors">
                                查看详细监控 →
                            </button>
                        </div>
                    </div>

                    <!-- 功能测试面板 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800">
                        <h4 class="font-semibold text-sm mb-3 text-yellow-400">🧪 功能测试</h4>
                        <div class="space-y-2">
                            <button onclick="testMainFunctions()" class="w-full p-2 bg-yellow-600/20 rounded hover:bg-yellow-600/30 transition-colors text-left">
                                <div class="text-sm font-medium text-yellow-400">测试主要功能</div>
                                <div class="text-xs text-gray-400">验证所有核心功能是否正常</div>
                            </button>
                            <button onclick="testAIFeatures()" class="w-full p-2 bg-purple-600/20 rounded hover:bg-purple-600/30 transition-colors text-left">
                                <div class="text-sm font-medium text-purple-400">测试AI功能</div>
                                <div class="text-xs text-gray-400">验证AI智能功能</div>
                            </button>
                        </div>
                    </div>

                    <!-- 费用分析图表 -->
                    <div id="cost-analysis" class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 hidden">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-sm">💰 费用分析</h4>
                            <button onclick="aiAgent.toggleCostAnalysis()" class="text-xs text-blue-400 hover:text-blue-300">
                                详细 →
                            </button>
                        </div>
                        <div class="space-y-3">
                            <!-- 费用对比条形图 -->
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-400">当前月费</span>
                                    <span class="text-red-400">¥6</span>
                                </div>
                                <div class="w-full bg-gray-800 rounded-full h-2">
                                    <div class="bg-red-400 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-400">优化后</span>
                                    <span class="text-green-400">¥2</span>
                                </div>
                                <div class="w-full bg-gray-800 rounded-full h-2">
                                    <div class="bg-green-400 h-2 rounded-full" style="width: 25%"></div>
                                </div>
                            </div>
                            <div class="text-center pt-2 border-t border-gray-700">
                                <div class="text-sm font-semibold text-green-400">年节省 ¥48</div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统监控面板 -->
                    <div id="system-monitoring" class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 hidden">
                        <h3 class="text-sm font-semibold mb-3 flex items-center" style="color: var(--text-primary);">
                            <svg class="w-4 h-4 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            系统监控
                        </h3>

                        <div class="space-y-3">
                            <!-- 系统状态 -->
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-xs text-gray-400">系统状态</span>
                                    <span class="text-xs text-green-400">正常运行</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-green-400 h-2 rounded-full" style="width: 98%"></div>
                                </div>
                                <div class="text-xs text-gray-400 mt-1">运行时间: 24小时7分钟</div>
                            </div>

                            <!-- AI响应性能 -->
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-xs text-gray-400">AI响应时间</span>
                                    <span class="text-xs text-blue-400">0.8秒</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-blue-400 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <div class="text-xs text-gray-400 mt-1">平均响应: 0.9秒</div>
                            </div>

                            <!-- 用户满意度 -->
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-xs text-gray-400">用户满意度</span>
                                    <span class="text-xs text-yellow-400">4.8/5.0</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-yellow-400 h-2 rounded-full" style="width: 96%"></div>
                                </div>
                                <div class="text-xs text-gray-400 mt-1">基于1,247个评价</div>
                            </div>

                            <!-- 今日统计 -->
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="text-xs text-gray-400 mb-2">今日统计</div>
                                <div class="grid grid-cols-2 gap-2 text-xs">
                                    <div class="text-center">
                                        <div class="text-blue-400 font-semibold">1,247</div>
                                        <div class="text-gray-400">对话次数</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-green-400 font-semibold">892</div>
                                        <div class="text-gray-400">成功解决</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-purple-400 font-semibold">156</div>
                                        <div class="text-gray-400">银信通开通</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-orange-400 font-semibold">234</div>
                                        <div class="text-gray-400">转账操作</div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button onclick="hideSystemMonitoring()" class="text-xs text-gray-400 hover:text-gray-300">
                                    收起监控面板
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部输入区域 -->
        <div class="backdrop-blur-xl sticky bottom-0" style="border-top: 1px solid var(--border-primary); background-color: var(--bg-primary);">
            <div class="max-w-7xl mx-auto px-4 py-4">
                <div class="flex items-center space-x-4" role="search" aria-label="消息输入区域">
                    <div class="flex-1 relative">
                        <label for="user-input" class="sr-only">输入您的问题或需求</label>
                        <input type="text" id="user-input"
                               placeholder="告诉我您需要什么帮助，比如：我想开通银信通、转账汇款、查询余额..."
                               class="w-full rounded-xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                               style="background-color: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-primary);"
                               aria-describedby="input-help"
                               autocomplete="off"
                               spellcheck="true">
                        <button id="voice-input" onclick="aiAgent.toggleVoiceInput()"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-blue-400 transition-colors focus:ring-2 focus:ring-blue-400 focus:outline-none rounded"
                                title="语音输入" aria-label="开启语音输入" tabindex="0">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                            </svg>
                        </button>
                    </div>
                    <button onclick="sendMessage()"
                            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-xl font-medium transition-all hover:scale-105 flex items-center space-x-2 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                            aria-label="发送消息" tabindex="0">
                        <span>发送</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </div>
                <div id="input-help" class="mt-2 text-xs text-gray-500 text-center" role="status" aria-live="polite">
                    按 Enter 发送消息 • 支持语音输入 • 智能银行助手24小时在线
                </div>

                <!-- 无障碍状态提示区域 -->
                <div id="accessibility-status" class="sr-only" aria-live="assertive" aria-atomic="true"></div>
            </div>
        </div>
    </div>

    <script src="scripts/app.js"></script>
    <script src="scripts/animations.js"></script>

    <!-- 初始化脚本 -->
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加 Enter 键发送消息支持
            const userInput = document.getElementById('user-input');
            if (userInput) {
                userInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }

            // 初始化欢迎消息
            if (typeof aiAgent !== 'undefined') {
                aiAgent.showWelcomeMessage();

                // 初始化无障碍功能
                aiAgent.initializeAccessibility();
            }

            // 键盘导航支持
            initializeKeyboardNavigation();

            // 高对比度模式检测
            if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
                document.body.classList.add('high-contrast');
            }

            // 减少动画偏好检测
            if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                document.body.classList.add('reduced-motion');
            }
        });

        // 键盘导航初始化
        function initializeKeyboardNavigation() {
            // 为所有服务按钮添加键盘事件
            const serviceButtons = document.querySelectorAll('.service-button');
            serviceButtons.forEach((button, index) => {
                button.addEventListener('keydown', function(e) {
                    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                        e.preventDefault();
                        const nextIndex = (index + 1) % serviceButtons.length;
                        serviceButtons[nextIndex].focus();
                    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                        e.preventDefault();
                        const prevIndex = (index - 1 + serviceButtons.length) % serviceButtons.length;
                        serviceButtons[prevIndex].focus();
                    } else if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        button.click();
                    }
                });
            });
        }

        // 无障碍状态通知
        function announceToScreenReader(message) {
            const statusElement = document.getElementById('accessibility-status');
            if (statusElement) {
                statusElement.textContent = message;
                // 清除消息以便下次通知
                setTimeout(() => {
                    statusElement.textContent = '';
                }, 1000);
            }
        }

        // 全局键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Alt + H: 显示帮助
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.showGeneralHelpMessage();
                    announceToScreenReader('帮助信息已显示');
                }
            }

            // Alt + S: 跳转到搜索框
            if (e.altKey && e.key === 's') {
                e.preventDefault();
                const userInput = document.getElementById('user-input');
                if (userInput) {
                    userInput.focus();
                    announceToScreenReader('已跳转到消息输入框');
                }
            }

            // Alt + M: 跳转到主要内容
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.focus();
                    announceToScreenReader('已跳转到对话区域');
                }
            }
        });

        // 演示控制相关函数
        window.toggleDemoControl = function() {
            const panel = document.getElementById('demo-control-panel');
            if (panel) {
                panel.classList.toggle('hidden');
            }
        };

        window.startDemo = function() {
            const scenario = document.getElementById('demo-scenario').value;
            const speed = document.getElementById('demo-speed').value;

            updateDemoStatus('演示启动中...');

            // 模拟演示启动
            setTimeout(() => {
                updateDemoStatus(`正在演示: ${getScenarioName(scenario)}`);

                // 启动相应的演示场景
                switch(scenario) {
                    case 'banking_services':
                        startBankingServicesDemo(speed);
                        break;
                    case 'ai_features':
                        startAIFeaturesDemo(speed);
                        break;
                    case 'accessibility':
                        startAccessibilityDemo(speed);
                        break;
                    case 'full_demo':
                        startFullDemo(speed);
                        break;
                }
            }, 1000);
        };

        window.stopDemo = function() {
            updateDemoStatus('演示已停止');
            // 清除所有演示定时器
            if (window.demoTimer) {
                clearTimeout(window.demoTimer);
                window.demoTimer = null;
            }
        };

        window.pauseDemo = function() {
            updateDemoStatus('演示已暂停');
        };

        window.resumeDemo = function() {
            updateDemoStatus('演示继续中...');
        };

        window.changeScenario = function(scenario) {
            updateDemoStatus(`已选择: ${getScenarioName(scenario)}`);
        };

        window.changeDemoSpeed = function(speed) {
            updateDemoStatus(`演示速度: ${speed}x`);
        };

        function updateDemoStatus(message) {
            const statusElement = document.getElementById('demo-status');
            if (statusElement) {
                statusElement.textContent = message;
            }
        }

        function getScenarioName(scenario) {
            const names = {
                'banking_services': '银行服务演示',
                'ai_features': 'AI功能演示',
                'accessibility': '无障碍功能演示',
                'full_demo': '完整功能演示'
            };
            return names[scenario] || scenario;
        }

        // 演示场景函数
        function startBankingServicesDemo(speed) {
            const delay = 3000 / speed;

            // 演示银信通服务
            window.demoTimer = setTimeout(() => {
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.handleSmsServiceRequest('sms_notification', '演示：银信通服务');
                }
            }, delay);

            // 演示转账功能
            window.demoTimer = setTimeout(() => {
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.handleTransferRequest('演示：转账功能');
                }
            }, delay * 2);

            // 演示账户查询
            window.demoTimer = setTimeout(() => {
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.handleAccountManagementRequest('演示：账户查询');
                }
            }, delay * 3);
        }

        function startAIFeaturesDemo(speed) {
            const delay = 3000 / speed;

            // 演示AI推荐
            window.demoTimer = setTimeout(() => {
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.showAllRecommendations();
                }
            }, delay);

            // 演示AI分析
            window.demoTimer = setTimeout(() => {
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.showThinking();
                }
            }, delay * 2);
        }

        function startAccessibilityDemo(speed) {
            const delay = 3000 / speed;

            // 演示语音功能
            window.demoTimer = setTimeout(() => {
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.toggleVoiceAnnouncement();
                }
            }, delay);

            // 演示无障碍模式
            window.demoTimer = setTimeout(() => {
                if (typeof aiAgent !== 'undefined') {
                    aiAgent.toggleAccessibilityMode();
                }
            }, delay * 2);
        }

        function startFullDemo(speed) {
            const delay = 2000 / speed;
            let step = 0;

            const demoSteps = [
                () => aiAgent.showWelcomeMessage(),
                () => aiAgent.handleSmsServiceRequest('sms_notification', '演示：银信通服务'),
                () => aiAgent.handleTransferRequest('演示：转账功能'),
                () => aiAgent.handleAccountManagementRequest('演示：账户查询'),
                () => aiAgent.showAllRecommendations(),
                () => aiAgent.toggleVoiceAnnouncement(),
                () => updateDemoStatus('完整演示结束')
            ];

            function runNextStep() {
                if (step < demoSteps.length && typeof aiAgent !== 'undefined') {
                    demoSteps[step]();
                    step++;
                    if (step < demoSteps.length) {
                        window.demoTimer = setTimeout(runNextStep, delay);
                    }
                }
            }

            runNextStep();
        }

        // 系统监控相关函数
        window.showSystemMonitoring = function() {
            const panel = document.getElementById('system-monitoring');
            if (panel) {
                panel.classList.remove('hidden');
                // 启动实时数据更新
                startMonitoringUpdates();
            }
        };

        window.hideSystemMonitoring = function() {
            const panel = document.getElementById('system-monitoring');
            if (panel) {
                panel.classList.add('hidden');
                // 停止实时数据更新
                stopMonitoringUpdates();
            }
        };

        let monitoringInterval;

        function startMonitoringUpdates() {
            // 每5秒更新一次监控数据
            monitoringInterval = setInterval(updateMonitoringData, 5000);
        }

        function stopMonitoringUpdates() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
        }

        function updateMonitoringData() {
            // 模拟实时数据更新
            const panel = document.getElementById('system-monitoring');
            if (!panel || panel.classList.contains('hidden')) return;

            // 更新系统状态
            const systemStatus = Math.floor(Math.random() * 5) + 95; // 95-99%
            const systemBar = panel.querySelector('.bg-green-400');
            if (systemBar) {
                systemBar.style.width = systemStatus + '%';
            }

            // 更新AI响应时间
            const responseTime = (Math.random() * 0.5 + 0.5).toFixed(1); // 0.5-1.0秒
            const responseTimeSpan = panel.querySelector('.text-blue-400');
            if (responseTimeSpan && responseTimeSpan.textContent.includes('秒')) {
                responseTimeSpan.textContent = responseTime + '秒';
            }

            // 更新对话次数
            const currentCount = parseInt(panel.querySelector('.text-blue-400.font-semibold').textContent.replace(',', ''));
            const newCount = currentCount + Math.floor(Math.random() * 3);
            panel.querySelector('.text-blue-400.font-semibold').textContent = newCount.toLocaleString();

            // 更新成功解决数
            const successCount = Math.floor(newCount * 0.72); // 约72%成功率
            const successElements = panel.querySelectorAll('.text-green-400.font-semibold');
            if (successElements.length > 0) {
                successElements[0].textContent = successCount.toLocaleString();
            }
        }

        // 功能测试函数
        window.testMainFunctions = function() {
            console.log('开始测试主要功能...');

            // 测试AI Agent是否正确初始化
            if (typeof aiAgent === 'undefined') {
                alert('❌ AI Agent未正确初始化');
                return;
            }

            // 测试主要函数是否存在
            const mainFunctions = [
                'sendMessage',
                'handleSmsServiceRequest',
                'handleTransferRequest',
                'handleAccountManagementRequest',
                'showGeneralHelpMessage',
                'contactHumanAgent',
                'toggleVoiceInput',
                'toggleAccessibilityMode'
            ];

            let missingFunctions = [];
            mainFunctions.forEach(funcName => {
                if (typeof aiAgent[funcName] !== 'function') {
                    missingFunctions.push(funcName);
                }
            });

            if (missingFunctions.length > 0) {
                alert('❌ 缺失函数: ' + missingFunctions.join(', '));
                return;
            }

            // 测试基本功能
            try {
                aiAgent.addAIMessage(`
                    <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-green-400">✅</span>
                            <span class="text-green-400">功能测试通过</span>
                        </div>
                        <p class="text-sm text-gray-300 mt-2">
                            所有主要功能都正常工作！测试项目：
                        </p>
                        <ul class="text-xs text-gray-400 mt-2 space-y-1">
                            <li>• AI Agent 初始化 ✅</li>
                            <li>• 核心函数定义 ✅</li>
                            <li>• 消息显示功能 ✅</li>
                            <li>• 银行服务功能 ✅</li>
                            <li>• 用户交互功能 ✅</li>
                        </ul>
                    </div>
                `);
                console.log('✅ 主要功能测试通过');
            } catch (error) {
                alert('❌ 测试过程中出现错误: ' + error.message);
                console.error('测试错误:', error);
            }
        };

        window.testAIFeatures = function() {
            console.log('开始测试AI功能...');

            if (typeof aiAgent === 'undefined') {
                alert('❌ AI Agent未正确初始化');
                return;
            }

            // 测试AI功能
            const aiFunctions = [
                'generatePersonalizedRecommendations',
                'analyzeIntentWithContext',
                'showAllRecommendations',
                'handleUserMessageWithContext',
                'initializeAccessibility'
            ];

            let missingAIFunctions = [];
            aiFunctions.forEach(funcName => {
                if (typeof aiAgent[funcName] !== 'function') {
                    missingAIFunctions.push(funcName);
                }
            });

            try {
                // 测试推荐系统
                const userProfile = aiAgent.getUserProfile();
                const accountData = aiAgent.getCurrentCard();
                const recommendations = aiAgent.generatePersonalizedRecommendations(userProfile, accountData);

                aiAgent.addAIMessage(`
                    <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-blue-400">🤖</span>
                            <span class="text-blue-400">AI功能测试结果</span>
                        </div>
                        <p class="text-sm text-gray-300 mt-2">
                            AI智能功能测试完成！
                        </p>
                        <ul class="text-xs text-gray-400 mt-2 space-y-1">
                            <li>• 个性化推荐系统 ${recommendations.length > 0 ? '✅' : '❌'}</li>
                            <li>• 用户档案分析 ${userProfile ? '✅' : '❌'}</li>
                            <li>• 账户数据获取 ${accountData ? '✅' : '❌'}</li>
                            <li>• 缺失AI函数: ${missingAIFunctions.length === 0 ? '无' : missingAIFunctions.join(', ')}</li>
                            <li>• 生成推荐数量: ${recommendations.length}个</li>
                        </ul>
                    </div>
                `);
                console.log('✅ AI功能测试完成');
            } catch (error) {
                alert('❌ AI功能测试出现错误: ' + error.message);
                console.error('AI测试错误:', error);
            }
        };
    </script>
</body>
</html>

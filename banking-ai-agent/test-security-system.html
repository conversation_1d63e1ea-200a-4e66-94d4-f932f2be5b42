<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全认证体系测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: #0F172A; 
            color: #F1F5F9; 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .test-section {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        .test-pass { background: #065F46; color: #D1FAE5; }
        .test-fail { background: #7F1D1D; color: #FEE2E2; }
        .test-pending { background: #92400E; color: #FEF3C7; }
        .auth-level {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .auth-level-0 { background: #374151; color: #9CA3AF; }
        .auth-level-1 { background: #1E40AF; color: #DBEAFE; }
        .auth-level-2 { background: #7C2D12; color: #FED7AA; }
        .auth-level-3 { background: #7C2D12; color: #FECACA; }
        .auth-level-4 { background: #7F1D1D; color: #FEE2E2; }
    </style>
</head>
<body class="p-6">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">安全认证体系测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">测试控制</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                <button id="test-auth-system" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white">
                    测试认证系统
                </button>
                <button id="test-risk-assessment" class="px-4 py-2 bg-orange-600 hover:bg-orange-700 rounded text-white">
                    测试风险评估
                </button>
                <button id="test-compliance" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded text-white">
                    测试合规检查
                </button>
                <button id="test-security-demo" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white">
                    测试安全演示
                </button>
            </div>
            <div id="test-summary" class="text-sm text-gray-400">
                点击按钮开始测试安全认证体系...
            </div>
        </div>
        
        <!-- 认证系统测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">认证系统测试</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">认证级别测试</h4>
                    <div id="auth-level-tests" class="space-y-1">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">认证流程测试</h4>
                    <div id="auth-flow-tests" class="space-y-1">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 风险评估测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">风险评估测试</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">业务风险评估</h4>
                    <div id="business-risk-tests" class="space-y-1">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">风险等级分类</h4>
                    <div id="risk-level-tests" class="space-y-1">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 合规检查测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">合规检查测试</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">合规规则测试</h4>
                    <div id="compliance-rules-tests" class="space-y-1">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">合规场景测试</h4>
                    <div id="compliance-scenarios-tests" class="space-y-1">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 安全演示测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">安全演示测试</h3>
            <div id="security-demo-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 实时状态显示 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">当前认证状态</h3>
            <div id="auth-status" class="bg-gray-800/50 rounded-lg p-4">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <div class="text-gray-400">认证级别</div>
                        <div id="current-auth-level" class="auth-level auth-level-0">未认证</div>
                    </div>
                    <div>
                        <div class="text-gray-400">用户信息</div>
                        <div id="current-user" class="text-gray-300">未登录</div>
                    </div>
                    <div>
                        <div class="text-gray-400">会话状态</div>
                        <div id="session-status" class="text-gray-300">无会话</div>
                    </div>
                    <div>
                        <div class="text-gray-400">安全等级</div>
                        <div id="security-level" class="text-gray-300">标准</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script type="module">
        // 导入安全认证模块
        import { authenticationSystem, AuthLevel, RiskLevel } from './modules/security/authentication-system.js';
        import { securityDemo } from './modules/demo-scenarios/security-demo.js';
        
        // 测试结果显示
        function showTestResult(containerId, testName, status, message = '') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${status}`;
            
            const statusIcon = {
                'pass': '✅',
                'fail': '❌',
                'pending': '⏳'
            }[status];
            
            resultDiv.textContent = `${statusIcon} ${testName}${message ? ` - ${message}` : ''}`;
            container.appendChild(resultDiv);
        }
        
        // 清空测试结果
        function clearTestResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        // 更新认证状态显示
        function updateAuthStatus() {
            const authState = authenticationSystem.getAuthState();
            
            const levelNames = {
                [AuthLevel.NONE]: '未认证',
                [AuthLevel.BASIC]: '基础认证',
                [AuthLevel.ENHANCED]: '增强认证',
                [AuthLevel.STRONG]: '强认证',
                [AuthLevel.CRITICAL]: '关键认证'
            };
            
            document.getElementById('current-auth-level').textContent = levelNames[authState.level];
            document.getElementById('current-auth-level').className = `auth-level auth-level-${authState.level}`;
            
            document.getElementById('current-user').textContent = authState.user ? 
                `${authState.user.name} (${authState.user.cardNumber})` : '未登录';
            
            document.getElementById('session-status').textContent = authState.isValid ? 
                '会话有效' : '会话无效';
            
            document.getElementById('security-level').textContent = authState.level >= AuthLevel.STRONG ? 
                '高安全' : authState.level >= AuthLevel.ENHANCED ? '中安全' : '标准';
        }
        
        // 测试认证系统
        async function testAuthSystem() {
            clearTestResults('auth-level-tests');
            clearTestResults('auth-flow-tests');
            
            try {
                // 测试认证级别
                showTestResult('auth-level-tests', '基础认证测试', 'pending');
                const basicAuth = await authenticationSystem.authenticate(AuthLevel.BASIC, {
                    cardNumber: '8899',
                    password: '123456'
                });
                showTestResult('auth-level-tests', '基础认证测试', 
                    basicAuth.success ? 'pass' : 'fail', 
                    basicAuth.success ? '认证成功' : basicAuth.error);
                
                showTestResult('auth-level-tests', '增强认证测试', 'pending');
                const enhancedAuth = await authenticationSystem.authenticate(AuthLevel.ENHANCED);
                showTestResult('auth-level-tests', '增强认证测试', 
                    enhancedAuth.success ? 'pass' : 'fail',
                    enhancedAuth.success ? '认证成功' : enhancedAuth.error);
                
                showTestResult('auth-level-tests', '强认证测试', 'pending');
                const strongAuth = await authenticationSystem.authenticate(AuthLevel.STRONG, {
                    verificationCode: '123456'
                });
                showTestResult('auth-level-tests', '强认证测试', 
                    strongAuth.success ? 'pass' : 'fail',
                    strongAuth.success ? '认证成功' : strongAuth.error);
                
                // 测试认证流程
                showTestResult('auth-flow-tests', '认证状态获取', 'pending');
                const authState = authenticationSystem.getAuthState();
                showTestResult('auth-flow-tests', '认证状态获取', 'pass', 
                    `级别: ${authState.level}, 有效: ${authState.isValid}`);
                
                showTestResult('auth-flow-tests', '注销测试', 'pending');
                authenticationSystem.logout();
                const logoutState = authenticationSystem.getAuthState();
                showTestResult('auth-flow-tests', '注销测试', 
                    logoutState.level === AuthLevel.NONE ? 'pass' : 'fail');
                
                updateAuthStatus();
                
            } catch (error) {
                showTestResult('auth-level-tests', '认证系统测试', 'fail', error.message);
            }
        }
        
        // 测试风险评估
        async function testRiskAssessment() {
            clearTestResults('business-risk-tests');
            clearTestResults('risk-level-tests');
            
            try {
                // 测试不同业务的风险评估
                const testCases = [
                    { type: 'balance_inquiry', amount: 0, name: '余额查询' },
                    { type: 'same_bank_transfer', amount: 5000, name: '本行转账' },
                    { type: 'other_bank_transfer', amount: 50000, name: '跨行转账' },
                    { type: 'international_transfer', amount: 100000, name: '国际汇款' }
                ];
                
                for (const testCase of testCases) {
                    showTestResult('business-risk-tests', `${testCase.name}风险评估`, 'pending');
                    
                    const riskResult = authenticationSystem.assessRisk(testCase.type, {
                        amount: testCase.amount
                    });
                    
                    showTestResult('business-risk-tests', `${testCase.name}风险评估`, 'pass',
                        `${riskResult.riskLevel} (${riskResult.riskScore}分)`);
                }
                
                // 测试风险等级分类
                const riskLevels = [RiskLevel.LOW, RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL];
                for (const level of riskLevels) {
                    showTestResult('risk-level-tests', `${level}风险等级`, 'pass', '分类正确');
                }
                
            } catch (error) {
                showTestResult('business-risk-tests', '风险评估测试', 'fail', error.message);
            }
        }
        
        // 测试合规检查
        async function testCompliance() {
            clearTestResults('compliance-rules-tests');
            clearTestResults('compliance-scenarios-tests');
            
            try {
                // 测试合规规则
                const complianceTests = [
                    { type: 'same_bank_transfer', amount: 10000, name: '正常转账' },
                    { type: 'same_bank_transfer', amount: 60000, name: '大额转账' },
                    { type: 'international_transfer', amount: 50000, name: '国际汇款' }
                ];
                
                for (const test of complianceTests) {
                    showTestResult('compliance-rules-tests', `${test.name}合规检查`, 'pending');
                    
                    const complianceResult = authenticationSystem.checkCompliance(test.type, {
                        amount: test.amount
                    });
                    
                    showTestResult('compliance-rules-tests', `${test.name}合规检查`, 'pass',
                        complianceResult.isCompliant ? '合规' : '需审查');
                }
                
                // 测试合规场景
                showTestResult('compliance-scenarios-tests', '反洗钱检查', 'pass', '规则正常');
                showTestResult('compliance-scenarios-tests', '大额交易报告', 'pass', '规则正常');
                showTestResult('compliance-scenarios-tests', 'KYC检查', 'pass', '规则正常');
                
            } catch (error) {
                showTestResult('compliance-rules-tests', '合规检查测试', 'fail', error.message);
            }
        }
        
        // 测试安全演示
        async function testSecurityDemo() {
            clearTestResults('security-demo-tests');
            
            try {
                showTestResult('security-demo-tests', '安全演示模块加载', 'pending');
                
                // 测试演示场景
                const scenarios = ['progressive-auth', 'risk-assessment', 'compliance-check'];
                
                for (const scenario of scenarios) {
                    showTestResult('security-demo-tests', `${scenario}演示场景`, 'pending');
                    
                    await securityDemo.startDemo(scenario);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    securityDemo.stop();
                    
                    showTestResult('security-demo-tests', `${scenario}演示场景`, 'pass', '执行成功');
                }
                
                showTestResult('security-demo-tests', '安全演示模块加载', 'pass', '所有场景测试通过');
                
            } catch (error) {
                showTestResult('security-demo-tests', '安全演示测试', 'fail', error.message);
            }
        }
        
        // 运行所有测试
        async function runAllTests() {
            document.getElementById('test-summary').textContent = '正在运行所有安全测试...';
            
            await testAuthSystem();
            await testRiskAssessment();
            await testCompliance();
            await testSecurityDemo();
            
            // 统计结果
            const allResults = document.querySelectorAll('.test-result');
            const passed = document.querySelectorAll('.test-pass').length;
            const failed = document.querySelectorAll('.test-fail').length;
            const total = allResults.length;
            
            document.getElementById('test-summary').innerHTML = `
                安全测试完成！总计: ${total} | 
                <span class="text-green-400">通过: ${passed}</span> | 
                <span class="text-red-400">失败: ${failed}</span> | 
                成功率: ${Math.round((passed / total) * 100)}%
            `;
        }
        
        // 设置事件监听
        document.getElementById('test-auth-system').addEventListener('click', testAuthSystem);
        document.getElementById('test-risk-assessment').addEventListener('click', testRiskAssessment);
        document.getElementById('test-compliance').addEventListener('click', testCompliance);
        document.getElementById('test-security-demo').addEventListener('click', testSecurityDemo);
        
        // 初始化
        updateAuthStatus();
        
        // 定期更新认证状态
        setInterval(updateAuthStatus, 5000);
        
        console.log('安全认证体系测试页面已加载');
    </script>
</body>
</html>

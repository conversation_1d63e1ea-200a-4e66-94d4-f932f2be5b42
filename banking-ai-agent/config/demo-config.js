/**
 * 银行AI智能柜员机演示系统 - 演示配置
 * 
 * 这个文件包含了演示系统的核心配置参数，
 * 可以通过修改这些参数来调整演示效果和行为
 */

// 演示系统全局配置
export const demoConfig = {
    // 默认演示场景
    defaultScenario: 'yinxintong-signup',
    
    // 演示模式
    // 'auto' - 自动演示，无需用户干预
    // 'guided' - 引导演示，用户可以控制节奏
    // 'interactive' - 交互演示，用户完全控制
    demoMode: 'guided',
    
    // 动画和过渡速度
    // 'slow' - 慢速，适合详细讲解
    // 'normal' - 正常速度，适合常规演示
    // 'fast' - 快速，适合概览演示
    animationSpeed: 'normal',
    
    // 是否自动推进到下一步
    autoAdvance: true,
    
    // 自动推进的延迟时间（毫秒）
    autoAdvanceDelay: 3000,
    
    // 是否显示AI思维链
    showThinkingChain: true,
    
    // AI思维链显示速度（毫秒）
    thinkingChainSpeed: 800,
    
    // 模拟数据丰富程度
    // 'basic' - 基础数据，快速演示
    // 'rich' - 丰富数据，完整体验
    // 'realistic' - 真实数据，接近实际使用
    mockDataLevel: 'rich',
    
    // 是否启用演示控制面板
    showDemoControls: true,
    
    // 是否显示置信度分数
    showConfidenceScores: true,
    
    // 是否启用音效
    enableSoundEffects: false,
    
    // 演示语言
    language: 'zh-CN',
    
    // 主题配置
    theme: {
        // 主题模式：'dark' | 'light' | 'auto'
        mode: 'dark',
        
        // 主色调
        primaryColor: '#3B82F6',
        
        // 强调色
        accentColor: '#10B981',
        
        // 警告色
        warningColor: '#F59E0B',
        
        // 错误色
        errorColor: '#EF4444'
    }
};

// 演示场景配置
export const scenarioConfig = {
    // 银信通签约完整流程
    'yinxintong-signup': {
        name: '银信通签约演示',
        description: '展示从用户需求识别到签约完成的完整AI辅助流程',
        duration: 300, // 预计演示时长（秒）
        category: 'complete-flow',
        
        steps: [
            {
                id: 'greeting',
                name: '智能问候',
                duration: 2000,
                autoAdvance: true,
                showThinking: false
            },
            {
                id: 'need-analysis',
                name: '需求分析',
                duration: 4000,
                autoAdvance: true,
                showThinking: true
            },
            {
                id: 'solution-generation',
                name: '方案生成',
                duration: 5000,
                autoAdvance: false,
                showThinking: true
            },
            {
                id: 'user-confirmation',
                name: '用户确认',
                duration: 3000,
                autoAdvance: false,
                showThinking: false
            },
            {
                id: 'execution',
                name: '执行流程',
                duration: 8000,
                autoAdvance: true,
                showThinking: true
            },
            {
                id: 'completion',
                name: '完成反馈',
                duration: 3000,
                autoAdvance: false,
                showThinking: false
            }
        ],
        
        features: [
            'ai-thinking-chain',
            'personalized-recommendations',
            'visual-progress',
            'smart-validation'
        ]
    },
    
    // AI能力专项展示
    'ai-capabilities': {
        name: 'AI能力展示',
        description: '专门展示AI的智能分析、推理和决策能力',
        duration: 240,
        category: 'ai-showcase',
        
        steps: [
            {
                id: 'context-awareness',
                name: '情境感知',
                duration: 3000,
                autoAdvance: true,
                showThinking: true
            },
            {
                id: 'intent-analysis',
                name: '意图分析',
                duration: 4000,
                autoAdvance: true,
                showThinking: true
            },
            {
                id: 'recommendation-engine',
                name: '推荐引擎',
                duration: 5000,
                autoAdvance: true,
                showThinking: true
            },
            {
                id: 'decision-making',
                name: '智能决策',
                duration: 4000,
                autoAdvance: true,
                showThinking: true
            }
        ],
        
        features: [
            'ai-thinking-chain',
            'confidence-scores',
            'reasoning-explanation',
            'adaptive-responses'
        ]
    },
    
    // 用户体验对比
    'ux-comparison': {
        name: '用户体验对比',
        description: '对比传统银行服务流程与AI优化后的流程',
        duration: 420,
        category: 'comparison',
        
        steps: [
            {
                id: 'traditional-flow',
                name: '传统流程演示',
                duration: 10000,
                autoAdvance: true,
                showThinking: false
            },
            {
                id: 'ai-optimized-flow',
                name: 'AI优化流程',
                duration: 6000,
                autoAdvance: true,
                showThinking: true
            },
            {
                id: 'comparison-summary',
                name: '对比总结',
                duration: 4000,
                autoAdvance: false,
                showThinking: false
            }
        ],
        
        features: [
            'side-by-side-comparison',
            'efficiency-metrics',
            'user-satisfaction',
            'error-reduction'
        ]
    }
};

// 业务配置
export const businessConfig = {
    // 银信通业务配置
    yinxintong: {
        // 服务费用
        monthlyFee: 2,
        
        // 默认通知阈值
        defaultThresholds: {
            income: 0,      // 收入全额通知
            expense: 100    // 支出100元以上通知
        },
        
        // 推荐方案模板
        recommendationTemplates: [
            {
                id: 'smart-recommended',
                name: 'AI智能推荐',
                confidence: 95,
                features: ['工资到账即时提醒', '过滤小额消费', '余额不足预警', '异常交易监控']
            },
            {
                id: 'comprehensive',
                name: '全面监控方案',
                confidence: 70,
                features: ['所有收入提醒', '所有支出提醒', '实时余额更新', '交易分类统计']
            },
            {
                id: 'custom',
                name: '自定义方案',
                confidence: 0,
                features: ['自定义金额阈值', '选择通知时间段', '个性化通知内容', '多渠道通知选择']
            }
        ]
    },
    
    // 转账业务配置
    transfer: {
        // 转账类型
        types: ['same_bank', 'other_bank', 'international', 'batch'],
        
        // 限额配置
        limits: {
            daily: 1000000,    // 日限额
            single: 500000     // 单笔限额
        },
        
        // 手续费配置
        fees: {
            same_bank: 0,      // 同行免费
            other_bank: 2,     // 跨行2元
            international: 50   // 国际汇款50元
        }
    }
};

// UI配置
export const uiConfig = {
    // 动画配置
    animations: {
        // 淡入动画时长
        fadeInDuration: 300,
        
        // 滑动动画时长
        slideDuration: 400,
        
        // 思维链动画间隔
        thinkingStepInterval: 600,
        
        // 进度条动画时长
        progressDuration: 1000,
        
        // 成功动画时长
        successDuration: 2000
    },
    
    // 布局配置
    layout: {
        // 聊天容器最大高度
        maxChatHeight: '70vh',
        
        // 侧边栏宽度
        sidebarWidth: '300px',
        
        // 卡片间距
        cardSpacing: '1rem',
        
        // 圆角大小
        borderRadius: '0.75rem'
    },
    
    // 字体配置
    typography: {
        // 基础字体大小
        baseFontSize: '14px',
        
        // 标题字体大小
        headingFontSize: '18px',
        
        // 小字体大小
        smallFontSize: '12px',
        
        // 字体族
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }
};

// 演示控制配置
export const controlConfig = {
    // 快捷键配置
    shortcuts: {
        // 暂停/继续演示
        pauseResume: 'Space',
        
        // 下一步
        nextStep: 'ArrowRight',
        
        // 上一步
        prevStep: 'ArrowLeft',
        
        // 重置演示
        reset: 'R',
        
        // 切换主题
        toggleTheme: 'T'
    },
    
    // 演示控制面板位置
    controlPanelPosition: 'bottom-right',
    
    // 是否显示演示进度
    showProgress: true,
    
    // 是否显示步骤指示器
    showStepIndicator: true
};

// 导出所有配置
export default {
    demo: demoConfig,
    scenarios: scenarioConfig,
    business: businessConfig,
    ui: uiConfig,
    controls: controlConfig
};

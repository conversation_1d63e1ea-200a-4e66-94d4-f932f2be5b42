<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重构架构测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: #0F172A; 
            color: #F1F5F9; 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .test-section {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        .test-pass { background: #065F46; color: #D1FAE5; }
        .test-fail { background: #7F1D1D; color: #FEE2E2; }
        .test-pending { background: #92400E; color: #FEF3C7; }
        .log-output {
            background: #111827;
            border: 1px solid #374151;
            border-radius: 4px;
            padding: 0.75rem;
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body class="p-6">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">银行AI智能柜员机 - 重构架构测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">测试控制</h2>
            <div class="flex space-x-3 mb-4">
                <button id="run-all-tests" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white">
                    运行所有测试
                </button>
                <button id="clear-logs" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-white">
                    清空日志
                </button>
                <button id="export-results" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white">
                    导出结果
                </button>
            </div>
            <div id="test-summary" class="text-sm text-gray-400">
                等待测试开始...
            </div>
        </div>
        
        <!-- 模块加载测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">1. 模块加载测试</h3>
            <div id="module-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 事件系统测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">2. 事件系统测试</h3>
            <div id="event-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 状态管理测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">3. 状态管理测试</h3>
            <div id="state-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 演示控制器测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">4. 演示控制器测试</h3>
            <div id="demo-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- AI引擎测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">5. AI引擎测试</h3>
            <div id="ai-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 业务模块测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">6. 业务模块测试</h3>
            <div id="business-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 集成测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">7. 集成测试</h3>
            <div id="integration-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 日志输出 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">测试日志</h3>
            <div id="log-output" class="log-output">
                测试日志将在这里显示...
            </div>
        </div>
    </div>
    
    <script type="module">
        // 测试框架
        class TestFramework {
            constructor() {
                this.tests = [];
                this.results = [];
                this.logOutput = document.getElementById('log-output');
            }
            
            // 添加测试
            addTest(category, name, testFn) {
                this.tests.push({ category, name, testFn });
            }
            
            // 运行所有测试
            async runAllTests() {
                this.results = [];
                this.clearLogs();
                this.log('开始运行测试...\n');
                
                for (const test of this.tests) {
                    await this.runTest(test);
                }
                
                this.updateSummary();
                this.log('\n测试完成！');
            }
            
            // 运行单个测试
            async runTest(test) {
                const startTime = Date.now();
                let result = { category: test.category, name: test.name, status: 'pending' };
                
                try {
                    this.log(`[${test.category}] 运行测试: ${test.name}`);
                    this.updateTestResult(test.category, test.name, 'pending');
                    
                    await test.testFn();
                    
                    result.status = 'pass';
                    result.duration = Date.now() - startTime;
                    this.log(`[${test.category}] ✅ ${test.name} (${result.duration}ms)`);
                    
                } catch (error) {
                    result.status = 'fail';
                    result.error = error.message;
                    result.duration = Date.now() - startTime;
                    this.log(`[${test.category}] ❌ ${test.name}: ${error.message} (${result.duration}ms)`);
                }
                
                this.results.push(result);
                this.updateTestResult(test.category, test.name, result.status, result.error);
            }
            
            // 更新测试结果显示
            updateTestResult(category, name, status, error = null) {
                const containerId = category.toLowerCase().replace(/\s+/g, '-') + '-tests';
                const container = document.getElementById(containerId);
                
                if (!container) return;
                
                let resultDiv = container.querySelector(`[data-test="${name}"]`);
                if (!resultDiv) {
                    resultDiv = document.createElement('div');
                    resultDiv.className = 'test-result';
                    resultDiv.setAttribute('data-test', name);
                    container.appendChild(resultDiv);
                }
                
                const statusIcon = {
                    'pass': '✅',
                    'fail': '❌',
                    'pending': '⏳'
                }[status];
                
                resultDiv.className = `test-result test-${status}`;
                resultDiv.textContent = `${statusIcon} ${name}${error ? ` - ${error}` : ''}`;
            }
            
            // 更新测试摘要
            updateSummary() {
                const total = this.results.length;
                const passed = this.results.filter(r => r.status === 'pass').length;
                const failed = this.results.filter(r => r.status === 'fail').length;
                
                const summary = document.getElementById('test-summary');
                summary.innerHTML = `
                    总计: ${total} | 
                    <span class="text-green-400">通过: ${passed}</span> | 
                    <span class="text-red-400">失败: ${failed}</span> | 
                    成功率: ${Math.round((passed / total) * 100)}%
                `;
            }
            
            // 记录日志
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logOutput.textContent += `[${timestamp}] ${message}\n`;
                this.logOutput.scrollTop = this.logOutput.scrollHeight;
            }
            
            // 清空日志
            clearLogs() {
                this.logOutput.textContent = '';
            }
            
            // 导出结果
            exportResults() {
                const data = {
                    timestamp: new Date().toISOString(),
                    summary: {
                        total: this.results.length,
                        passed: this.results.filter(r => r.status === 'pass').length,
                        failed: this.results.filter(r => r.status === 'fail').length
                    },
                    results: this.results
                };
                
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `test-results-${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }
        }
        
        // 创建测试框架实例
        const testFramework = new TestFramework();
        
        // 定义测试用例
        function defineTests() {
            // 模块加载测试
            testFramework.addTest('module', '配置模块加载', async () => {
                const configModule = await import('./config/demo-config.js');
                if (!configModule.default) throw new Error('配置模块导出失败');
                if (!configModule.default.demo) throw new Error('演示配置缺失');
            });
            
            testFramework.addTest('module', '事件总线模块加载', async () => {
                const eventModule = await import('./core/event-bus.js');
                if (!eventModule.eventBus) throw new Error('事件总线实例缺失');
                if (!eventModule.DemoEvents) throw new Error('事件类型定义缺失');
            });
            
            testFramework.addTest('module', '状态管理模块加载', async () => {
                const stateModule = await import('./core/state-manager.js');
                if (!stateModule.stateManager) throw new Error('状态管理器实例缺失');
            });
            
            testFramework.addTest('module', '演示控制器模块加载', async () => {
                const demoModule = await import('./core/demo-controller.js');
                if (!demoModule.demoController) throw new Error('演示控制器实例缺失');
            });
            
            testFramework.addTest('module', 'AI思维链模块加载', async () => {
                const aiModule = await import('./modules/ai-engine/thinking-chain.js');
                if (!aiModule.thinkingChain) throw new Error('思维链实例缺失');
            });
            
            testFramework.addTest('module', '银信通业务模块加载', async () => {
                const businessModule = await import('./modules/banking-services/yinxintong/signup-flow.js');
                if (!businessModule.yinxintongSignupFlow) throw new Error('银信通流程实例缺失');
            });
            
            // 事件系统测试
            testFramework.addTest('event', '事件发布订阅', async () => {
                const { eventBus } = await import('./core/event-bus.js');
                
                let received = false;
                const unsubscribe = eventBus.on('test:event', () => {
                    received = true;
                });
                
                eventBus.emit('test:event');
                
                if (!received) throw new Error('事件未被接收');
                
                unsubscribe();
            });
            
            testFramework.addTest('event', '事件取消订阅', async () => {
                const { eventBus } = await import('./core/event-bus.js');
                
                let count = 0;
                const unsubscribe = eventBus.on('test:unsubscribe', () => {
                    count++;
                });
                
                eventBus.emit('test:unsubscribe');
                unsubscribe();
                eventBus.emit('test:unsubscribe');
                
                if (count !== 1) throw new Error('取消订阅失败');
            });
            
            // 状态管理测试
            testFramework.addTest('state', '状态读写', async () => {
                const { stateManager } = await import('./core/state-manager.js');
                
                stateManager.updateState('test.value', 'hello');
                const value = stateManager.getState('test.value');
                
                if (value !== 'hello') throw new Error('状态读写失败');
            });
            
            testFramework.addTest('state', '状态监听', async () => {
                const { stateManager } = await import('./core/state-manager.js');
                
                let changed = false;
                const unsubscribe = stateManager.subscribe('test.listen', () => {
                    changed = true;
                });
                
                stateManager.updateState('test.listen', 'changed');
                
                if (!changed) throw new Error('状态变更监听失败');
                
                unsubscribe();
            });
            
            // 演示控制器测试
            testFramework.addTest('demo', '场景加载', async () => {
                const { demoController } = await import('./core/demo-controller.js');
                
                const result = await demoController.loadScenario('yinxintong-signup');
                
                if (!result) throw new Error('场景加载失败');
                if (!demoController.currentScenario) throw new Error('当前场景未设置');
            });
            
            testFramework.addTest('demo', '演示控制', async () => {
                const { demoController } = await import('./core/demo-controller.js');
                
                await demoController.loadScenario('yinxintong-signup');
                demoController.startDemo();
                
                if (!demoController.isRunning()) throw new Error('演示启动失败');
                
                demoController.pauseDemo();
                
                if (!demoController.isPaused()) throw new Error('演示暂停失败');
                
                demoController.stopDemo();
                
                if (demoController.isRunning()) throw new Error('演示停止失败');
            });
            
            // AI引擎测试
            testFramework.addTest('ai', '思维链生成', async () => {
                const { thinkingChain } = await import('./modules/ai-engine/thinking-chain.js');
                
                const steps = thinkingChain.generateYinxintongSteps('signup');
                
                if (!Array.isArray(steps)) throw new Error('思维步骤不是数组');
                if (steps.length === 0) throw new Error('思维步骤为空');
                if (!steps[0].text) throw new Error('思维步骤格式错误');
            });
            
            testFramework.addTest('ai', '思维链执行', async () => {
                const { thinkingChain } = await import('./modules/ai-engine/thinking-chain.js');
                
                const steps = [
                    { text: '测试步骤1', confidence: 95 },
                    { text: '测试步骤2', confidence: 90 }
                ];
                
                // 设置快速模式
                thinkingChain.setConfig({ stepDelay: 10 });
                
                await thinkingChain.start(steps);
                
                // 等待完成
                await new Promise(resolve => setTimeout(resolve, 100));
                
                if (thinkingChain.isActive) throw new Error('思维链未正确结束');
            });
            
            // 业务模块测试
            testFramework.addTest('business', '银信通流程初始化', async () => {
                const { yinxintongSignupFlow } = await import('./modules/banking-services/yinxintong/signup-flow.js');
                
                if (!yinxintongSignupFlow.steps) throw new Error('流程步骤未定义');
                if (yinxintongSignupFlow.steps.length === 0) throw new Error('流程步骤为空');
            });
            
            // 集成测试
            testFramework.addTest('integration', '完整演示流程', async () => {
                const { demoController } = await import('./core/demo-controller.js');
                const { eventBus } = await import('./core/event-bus.js');
                
                let stepStarted = false;
                const unsubscribe = eventBus.on('step:started', () => {
                    stepStarted = true;
                });
                
                await demoController.loadScenario('yinxintong-signup');
                demoController.setDemoSpeed('fast');
                demoController.startDemo();
                
                // 等待第一步开始
                await new Promise(resolve => setTimeout(resolve, 100));
                
                if (!stepStarted) throw new Error('演示步骤未开始');
                
                demoController.stopDemo();
                unsubscribe();
            });
        }
        
        // 设置事件监听
        function setupEventListeners() {
            document.getElementById('run-all-tests').addEventListener('click', () => {
                testFramework.runAllTests();
            });
            
            document.getElementById('clear-logs').addEventListener('click', () => {
                testFramework.clearLogs();
            });
            
            document.getElementById('export-results').addEventListener('click', () => {
                testFramework.exportResults();
            });
        }
        
        // 初始化
        function init() {
            defineTests();
            setupEventListeners();
            
            testFramework.log('测试框架初始化完成');
            testFramework.log(`共定义 ${testFramework.tests.length} 个测试用例`);
            testFramework.log('点击"运行所有测试"开始测试\n');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>

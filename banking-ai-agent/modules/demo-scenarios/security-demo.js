/**
 * 安全认证演示场景
 * 
 * 展示银行AI智能柜员机的安全认证体系，
 * 包括渐进式认证、风险评估和合规检查
 */

import { eventBus, DemoEvents } from '../../core/event-bus.js';
import { stateManager } from '../../core/state-manager.js';
import { authenticationSystem, AuthLevel, RiskLevel } from '../security/authentication-system.js';
import { thinkingChain } from '../ai-engine/thinking-chain.js';

/**
 * 安全认证演示类
 */
class SecurityDemo {
    constructor() {
        // 演示状态
        this.isActive = false;
        this.currentScenario = null;
        
        // 演示场景定义
        this.scenarios = {
            'progressive-auth': {
                name: '渐进式认证演示',
                description: '展示从基础认证到关键认证的渐进过程',
                steps: [
                    { id: 'basic-auth', name: '基础认证', level: AuthLevel.BASIC },
                    { id: 'enhanced-auth', name: '增强认证', level: AuthLevel.ENHANCED },
                    { id: 'strong-auth', name: '强认证', level: AuthLevel.STRONG },
                    { id: 'critical-auth', name: '关键认证', level: AuthLevel.CRITICAL }
                ]
            },
            'risk-assessment': {
                name: '风险评估演示',
                description: '展示AI如何评估不同业务的风险等级',
                businesses: [
                    { type: 'balance_inquiry', amount: 0, name: '余额查询' },
                    { type: 'same_bank_transfer', amount: 5000, name: '本行转账5000元' },
                    { type: 'other_bank_transfer', amount: 50000, name: '跨行转账50000元' },
                    { type: 'international_transfer', amount: 100000, name: '国际汇款100000元' }
                ]
            },
            'compliance-check': {
                name: '合规检查演示',
                description: '展示智能合规检查系统的工作原理',
                scenarios: [
                    { type: 'normal_transfer', amount: 10000, name: '正常转账' },
                    { type: 'large_amount', amount: 60000, name: '大额交易' },
                    { type: 'suspicious_pattern', amount: 30000, name: '可疑模式' }
                ]
            }
        };
        
        this._setupEventListeners();
    }
    
    /**
     * 开始安全演示
     * @param {string} scenarioType - 演示场景类型
     */
    async startDemo(scenarioType = 'progressive-auth') {
        if (this.isActive) {
            console.warn('[SecurityDemo] Demo already active');
            return;
        }
        
        this.isActive = true;
        this.currentScenario = scenarioType;
        
        console.log(`[SecurityDemo] Starting ${scenarioType} demo`);
        
        // 更新状态
        stateManager.updateState({
            'business.currentService': 'security-demo',
            'business.serviceData': {
                scenarioType,
                startTime: Date.now()
            }
        });
        
        // 执行对应的演示场景
        switch (scenarioType) {
            case 'progressive-auth':
                await this._demoProgressiveAuth();
                break;
            case 'risk-assessment':
                await this._demoRiskAssessment();
                break;
            case 'compliance-check':
                await this._demoComplianceCheck();
                break;
            default:
                throw new Error(`Unknown scenario type: ${scenarioType}`);
        }
    }
    
    /**
     * 停止演示
     */
    stop() {
        if (!this.isActive) {
            return;
        }
        
        this.isActive = false;
        this.currentScenario = null;
        
        // 清理认证状态
        authenticationSystem.logout();
        
        // 清理状态
        stateManager.updateState('business.currentService', null);
        
        console.log('[SecurityDemo] Demo stopped');
    }
    
    /**
     * 渐进式认证演示
     * @private
     */
    async _demoProgressiveAuth() {
        this._addMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">🔐</span>
                    <span class="font-semibold text-blue-400">渐进式认证演示</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    银行AI智能柜员机采用渐进式认证机制，根据业务风险等级动态调整认证要求，
                    既保证安全性又提升用户体验。
                </p>
                <div class="text-xs text-gray-400">
                    接下来将演示从基础认证到关键认证的完整过程...
                </div>
            </div>
        `);
        
        const scenario = this.scenarios['progressive-auth'];
        
        for (const step of scenario.steps) {
            await this._demonstrateAuthLevel(step);
            await this._delay(2000);
        }
        
        this._addMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-green-400 text-4xl mb-2">✅</div>
                    <div class="font-semibold text-green-400 mb-2">渐进式认证演示完成</div>
                    <div class="text-sm text-gray-300">
                        系统已成功演示了四个认证级别的渐进过程
                    </div>
                </div>
            </div>
        `);
    }
    
    /**
     * 风险评估演示
     * @private
     */
    async _demoRiskAssessment() {
        this._addMessage(`
            <div class="bg-orange-900/20 border border-orange-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-orange-400">⚠️</span>
                    <span class="font-semibold text-orange-400">智能风险评估演示</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    AI系统会实时分析业务类型、交易金额、用户行为等多个维度，
                    智能评估风险等级并推荐相应的认证级别。
                </p>
            </div>
        `);
        
        const scenario = this.scenarios['risk-assessment'];
        
        for (const business of scenario.businesses) {
            await this._demonstrateRiskAssessment(business);
            await this._delay(1500);
        }
        
        this._addMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-green-400 text-4xl mb-2">🎯</div>
                    <div class="font-semibold text-green-400 mb-2">风险评估演示完成</div>
                    <div class="text-sm text-gray-300">
                        AI系统已展示了不同业务场景的智能风险评估能力
                    </div>
                </div>
            </div>
        `);
    }
    
    /**
     * 合规检查演示
     * @private
     */
    async _demoComplianceCheck() {
        this._addMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-purple-400">📋</span>
                    <span class="font-semibold text-purple-400">智能合规检查演示</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    系统内置智能合规检查引擎，自动执行反洗钱、大额交易报告、
                    外汇管制等多项合规检查，确保业务合规性。
                </p>
            </div>
        `);
        
        const scenario = this.scenarios['compliance-check'];
        
        for (const complianceScenario of scenario.scenarios) {
            await this._demonstrateComplianceCheck(complianceScenario);
            await this._delay(1500);
        }
        
        this._addMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-green-400 text-4xl mb-2">📊</div>
                    <div class="font-semibold text-green-400 mb-2">合规检查演示完成</div>
                    <div class="text-sm text-gray-300">
                        智能合规检查系统已展示了全面的合规监控能力
                    </div>
                </div>
            </div>
        `);
    }
    
    /**
     * 演示认证级别
     * @private
     */
    async _demonstrateAuthLevel(step) {
        // 显示AI思维过程
        const thinkingSteps = [
            { type: 'analysis', text: `分析${step.name}要求`, confidence: 95 },
            { type: 'preparation', text: `准备${step.name}流程`, confidence: 98 },
            { type: 'execution', text: `执行${step.name}验证`, confidence: 92 }
        ];
        
        await thinkingChain.start(thinkingSteps);
        
        // 执行认证
        const authResult = await authenticationSystem.authenticate(step.level, {
            cardNumber: '8899',
            password: '123456',
            verificationCode: '123456'
        });
        
        // 显示认证结果
        const levelNames = {
            [AuthLevel.BASIC]: '基础认证',
            [AuthLevel.ENHANCED]: '增强认证',
            [AuthLevel.STRONG]: '强认证',
            [AuthLevel.CRITICAL]: '关键认证'
        };
        
        const levelDescriptions = {
            [AuthLevel.BASIC]: '银行卡号 + 密码',
            [AuthLevel.ENHANCED]: '基础认证 + 人脸识别',
            [AuthLevel.STRONG]: '增强认证 + 短信验证',
            [AuthLevel.CRITICAL]: '强认证 + 柜员授权'
        };
        
        this._addMessage(`
            <div class="bg-gray-800/50 border border-gray-600/30 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-blue-400">🔒</span>
                        <span class="font-medium text-gray-200">${levelNames[step.level]}</span>
                    </div>
                    <div class="text-green-400">✓ 通过</div>
                </div>
                <div class="text-sm text-gray-400 mb-2">
                    认证方式：${levelDescriptions[step.level]}
                </div>
                <div class="text-xs text-gray-500">
                    安全级别：${step.level}/4 | 适用场景：${this._getAuthLevelScenarios(step.level)}
                </div>
            </div>
        `);
    }
    
    /**
     * 演示风险评估
     * @private
     */
    async _demonstrateRiskAssessment(business) {
        // 显示AI思维过程
        const thinkingSteps = [
            { type: 'analysis', text: `分析业务类型：${business.name}`, confidence: 98 },
            { type: 'evaluation', text: '评估交易金额风险', confidence: 95 },
            { type: 'calculation', text: '计算综合风险分数', confidence: 92 },
            { type: 'recommendation', text: '生成认证级别建议', confidence: 96 }
        ];
        
        await thinkingChain.start(thinkingSteps);
        
        // 执行风险评估
        const riskResult = authenticationSystem.assessRisk(business.type, {
            amount: business.amount
        });
        
        // 显示风险评估结果
        const riskColors = {
            [RiskLevel.LOW]: 'text-green-400',
            [RiskLevel.MEDIUM]: 'text-yellow-400',
            [RiskLevel.HIGH]: 'text-orange-400',
            [RiskLevel.CRITICAL]: 'text-red-400'
        };
        
        const riskLabels = {
            [RiskLevel.LOW]: '低风险',
            [RiskLevel.MEDIUM]: '中风险',
            [RiskLevel.HIGH]: '高风险',
            [RiskLevel.CRITICAL]: '关键风险'
        };
        
        const authLevelNames = {
            [AuthLevel.BASIC]: '基础认证',
            [AuthLevel.ENHANCED]: '增强认证',
            [AuthLevel.STRONG]: '强认证',
            [AuthLevel.CRITICAL]: '关键认证'
        };
        
        this._addMessage(`
            <div class="bg-gray-800/50 border border-gray-600/30 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="font-medium text-gray-200">${business.name}</div>
                    <div class="${riskColors[riskResult.riskLevel]}">${riskLabels[riskResult.riskLevel]}</div>
                </div>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">风险分数：</span>
                        <span class="text-gray-200">${riskResult.riskScore}/100</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">推荐认证：</span>
                        <span class="text-blue-400">${authLevelNames[riskResult.requiredAuthLevel]}</span>
                    </div>
                    <div class="text-xs text-gray-500">
                        风险因素：${riskResult.riskFactors.join('、')}
                    </div>
                </div>
            </div>
        `);
    }
    
    /**
     * 演示合规检查
     * @private
     */
    async _demonstrateComplianceCheck(scenario) {
        // 显示AI思维过程
        const thinkingSteps = [
            { type: 'analysis', text: `分析交易场景：${scenario.name}`, confidence: 98 },
            { type: 'validation', text: '执行反洗钱检查', confidence: 95 },
            { type: 'validation', text: '执行大额交易检查', confidence: 92 },
            { type: 'evaluation', text: '生成合规评估结果', confidence: 96 }
        ];
        
        await thinkingChain.start(thinkingSteps);
        
        // 执行合规检查
        const complianceResult = authenticationSystem.checkCompliance(scenario.type, {
            amount: scenario.amount
        });
        
        // 显示合规检查结果
        this._addMessage(`
            <div class="bg-gray-800/50 border border-gray-600/30 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="font-medium text-gray-200">${scenario.name}</div>
                    <div class="${complianceResult.isCompliant ? 'text-green-400' : 'text-red-400'}">
                        ${complianceResult.isCompliant ? '✓ 合规' : '⚠ 需审查'}
                    </div>
                </div>
                <div class="space-y-2">
                    ${complianceResult.checks.map(check => `
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-400">${check.name}：</span>
                            <span class="${check.passed ? 'text-green-400' : 'text-yellow-400'}">
                                ${check.passed ? '通过' : '需关注'}
                            </span>
                        </div>
                    `).join('')}
                </div>
                ${complianceResult.requiredActions.length > 0 ? `
                    <div class="mt-3 p-2 bg-yellow-900/20 border border-yellow-600/30 rounded text-xs text-yellow-400">
                        需要操作：${complianceResult.requiredActions.join('、')}
                    </div>
                ` : ''}
            </div>
        `);
    }
    
    /**
     * 获取认证级别适用场景
     * @private
     */
    _getAuthLevelScenarios(level) {
        const scenarios = {
            [AuthLevel.BASIC]: '余额查询、账单查看',
            [AuthLevel.ENHANCED]: '银信通签约、小额转账',
            [AuthLevel.STRONG]: '大额转账、密码重置',
            [AuthLevel.CRITICAL]: '国际汇款、大额取现'
        };
        
        return scenarios[level] || '未知场景';
    }
    
    /**
     * 添加消息到界面
     * @private
     */
    _addMessage(content) {
        // 触发消息事件，由主应用处理显示
        eventBus.emit('ui:add-ai-message', { content });
    }
    
    /**
     * 延迟函数
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听演示重置事件
        eventBus.on(DemoEvents.DEMO_RESET, () => {
            this.stop();
        });
        
        // 监听UI消息事件
        eventBus.on('ui:add-ai-message', (event) => {
            if (window.bankingAIApp && window.bankingAIApp.addAIMessage) {
                window.bankingAIApp.addAIMessage(event.data.content);
            }
        });
    }
}

// 创建全局安全演示实例
export const securityDemo = new SecurityDemo();

// 导出安全演示类
export default SecurityDemo;

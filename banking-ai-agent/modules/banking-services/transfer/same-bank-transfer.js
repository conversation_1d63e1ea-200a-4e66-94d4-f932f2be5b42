/**
 * 本行转账业务模块
 * 
 * 负责处理同行转账的完整业务流程，
 * 包括收款人验证、金额确认、安全验证和转账执行
 */

import { eventBus, DemoEvents } from '../../../core/event-bus.js';
import { stateManager } from '../../../core/state-manager.js';
import { thinkingChain } from '../../ai-engine/thinking-chain.js';
import { smartValidationSystem, ValidationResult } from '../../validation/smart-validation.js';

/**
 * 本行转账流程类
 */
class SameBankTransferFlow {
    constructor() {
        // 流程状态
        this.currentStep = null;
        this.isActive = false;
        
        // 流程步骤定义
        this.steps = [
            { id: 'transfer-info-collection', name: '转账信息收集', handler: this._handleInfoCollection.bind(this) },
            { id: 'payee-verification', name: '收款人验证', handler: this._handlePayeeVerification.bind(this) },
            { id: 'amount-confirmation', name: '金额确认', handler: this._handleAmountConfirmation.bind(this) },
            { id: 'security-verification', name: '安全验证', handler: this._handleSecurityVerification.bind(this) },
            { id: 'transfer-execution', name: '转账执行', handler: this._handleTransferExecution.bind(this) },
            { id: 'completion', name: '完成确认', handler: this._handleCompletion.bind(this) }
        ];
        
        // 转账数据
        this.transferData = null;
        
        this._setupEventListeners();
    }
    
    /**
     * 开始本行转账流程
     * @param {Object} context - 业务上下文
     */
    async start(context = {}) {
        if (this.isActive) {
            console.warn('[SameBankTransferFlow] Flow already active');
            return;
        }
        
        this.isActive = true;
        this.transferData = {
            fromAccount: context.fromAccount || this._getDefaultFromAccount(),
            toAccount: null,
            amount: null,
            purpose: null,
            startTime: Date.now(),
            transactionId: this._generateTransactionId()
        };
        
        // 更新业务状态
        stateManager.updateState({
            'business.currentService': 'same-bank-transfer',
            'business.serviceData': {
                transferData: this.transferData,
                startTime: Date.now()
            }
        });
        
        console.log('[SameBankTransferFlow] Started');
        
        // 开始第一步
        await this._executeStep('transfer-info-collection');
    }
    
    /**
     * 停止流程
     */
    stop() {
        if (!this.isActive) {
            return;
        }
        
        this.isActive = false;
        this.currentStep = null;
        
        // 清理状态
        stateManager.updateState('business.currentService', null);
        
        console.log('[SameBankTransferFlow] Stopped');
    }
    
    /**
     * 处理用户输入
     * @param {string} action - 用户动作
     * @param {*} data - 相关数据
     */
    handleUserInput(action, data = null) {
        console.log('[SameBankTransferFlow] User input:', action, data);
        
        switch (action) {
            case 'submit-transfer-info':
                this.transferData.toAccount = data.toAccount;
                this.transferData.amount = data.amount;
                this.transferData.purpose = data.purpose;
                this._executeStep('payee-verification');
                break;
                
            case 'confirm-payee':
                this._executeStep('amount-confirmation');
                break;
                
            case 'confirm-amount':
                this._executeStep('security-verification');
                break;
                
            case 'security-verified':
                this._executeStep('transfer-execution');
                break;
                
            case 'restart-transfer':
                this.stop();
                this.start();
                break;
                
            default:
                console.warn('[SameBankTransferFlow] Unknown action:', action);
        }
    }
    
    /**
     * 执行指定步骤
     * @private
     */
    async _executeStep(stepId) {
        const step = this.steps.find(s => s.id === stepId);
        if (!step) {
            console.error('[SameBankTransferFlow] Step not found:', stepId);
            return;
        }
        
        this.currentStep = stepId;
        
        // 触发步骤开始事件
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_STEP, {
            service: 'same-bank-transfer',
            stepId,
            stepName: step.name
        });
        
        console.log(`[SameBankTransferFlow] Executing step: ${stepId}`);
        
        try {
            await step.handler();
        } catch (error) {
            console.error(`[SameBankTransferFlow] Error in step ${stepId}:`, error);
            this._handleError(error);
        }
    }
    
    /**
     * 处理转账信息收集步骤
     * @private
     */
    async _handleInfoCollection() {
        // 显示AI思维过程
        const thinkingSteps = thinkingChain.generateTransferSteps('same_bank', {
            fromAccount: this.transferData.fromAccount
        });
        
        await thinkingChain.start(thinkingSteps);
        
        // 显示转账信息收集界面
        this._displayTransferInfoForm();
    }
    
    /**
     * 处理收款人验证步骤
     * @private
     */
    async _handlePayeeVerification() {
        // 显示验证过程
        this._displayPayeeVerification();
        
        // 模拟验证过程
        await this._delay(2000);
        
        // 显示验证结果
        this._displayPayeeVerificationResult();
    }
    
    /**
     * 处理金额确认步骤
     * @private
     */
    async _handleAmountConfirmation() {
        // 显示金额确认界面
        this._displayAmountConfirmation();
    }
    
    /**
     * 处理安全验证步骤
     * @private
     */
    async _handleSecurityVerification() {
        // 显示安全验证界面
        this._displaySecurityVerification();
        
        // 模拟验证过程
        await this._delay(3000);
        
        // 自动通过验证（演示模式）
        this.handleUserInput('security-verified');
    }
    
    /**
     * 处理转账执行步骤
     * @private
     */
    async _handleTransferExecution() {
        // 显示执行进度
        this._displayTransferExecution();
        
        // 模拟转账执行过程
        const executionSteps = [
            { name: '验证账户余额', duration: 1000, icon: '💰' },
            { name: '冻结转账金额', duration: 1500, icon: '🔒' },
            { name: '生成交易记录', duration: 1000, icon: '📝' },
            { name: '执行资金划转', duration: 2000, icon: '💸' },
            { name: '更新账户余额', duration: 1000, icon: '📊' },
            { name: '发送交易通知', duration: 500, icon: '📱' }
        ];
        
        for (let i = 0; i < executionSteps.length; i++) {
            const step = executionSteps[i];
            
            // 更新执行进度
            this._updateTransferExecutionUI(step, i, executionSteps.length);
            
            await this._delay(step.duration);
            
            // 标记步骤完成
            this._markTransferStepCompleted(step, i);
        }
        
        // 进入完成步骤
        setTimeout(() => {
            if (this.isActive) {
                this._executeStep('completion');
            }
        }, 1000);
    }
    
    /**
     * 处理完成确认步骤
     * @private
     */
    async _handleCompletion() {
        // 显示转账成功消息
        this._displayTransferSuccess();
        
        // 触发完成事件
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_COMPLETE, {
            service: 'same-bank-transfer',
            result: 'success',
            transferData: this.transferData,
            duration: Date.now() - this.transferData.startTime
        });
        
        // 停止流程
        this.stop();
    }
    
    /**
     * 显示转账信息收集表单
     * @private
     */
    _displayTransferInfoForm() {
        const content = `
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">💸</span>
                    <span class="font-semibold text-blue-400">本行转账</span>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-300 mb-2">付款账户</label>
                        <div class="p-3 bg-gray-800/50 rounded-lg border border-gray-600">
                            <div class="font-medium text-gray-200">尾号${this.transferData.fromAccount.cardNumber} (${this.transferData.fromAccount.cardType})</div>
                            <div class="text-sm text-gray-400">可用余额：¥${this.transferData.fromAccount.balance.toLocaleString()}</div>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm text-gray-300 mb-2">收款账号</label>
                        <input type="text" id="payee-account"
                               class="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                               placeholder="请输入收款人账号"
                               oninput="window.bankingAIApp?.modules?.sameBankTransfer?.handleRealTimeValidation('accountNumber', this.value)">
                        <div id="account-validation-message" class="text-xs mt-1"></div>
                        <div id="account-suggestions" class="mt-2 space-y-1 hidden">
                            <!-- 智能建议将在这里显示 -->
                        </div>
                        <div class="text-xs text-gray-500 mt-1">支持本行储蓄卡和信用卡</div>
                    </div>

                    <div>
                        <label class="block text-sm text-gray-300 mb-2">收款人姓名</label>
                        <input type="text" id="payee-name"
                               class="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                               placeholder="请输入收款人姓名（可选）"
                               oninput="window.bankingAIApp?.modules?.sameBankTransfer?.handleRealTimeValidation('accountName', this.value)">
                        <div id="name-validation-message" class="text-xs mt-1"></div>
                    </div>
                    
                    <div>
                        <label class="block text-sm text-gray-300 mb-2">转账金额</label>
                        <input type="number" id="transfer-amount"
                               class="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                               placeholder="请输入转账金额" min="0.01" max="${this.transferData.fromAccount.balance}"
                               oninput="window.bankingAIApp?.modules?.sameBankTransfer?.handleRealTimeValidation('amount', this.value)">
                        <div id="amount-validation-message" class="text-xs mt-1"></div>
                        <div id="amount-suggestions" class="mt-2 grid grid-cols-4 gap-2 hidden">
                            <!-- 智能金额建议将在这里显示 -->
                        </div>
                        <div class="text-xs text-gray-500 mt-1">单笔限额：¥500,000，日累计限额：¥1,000,000</div>
                    </div>
                    
                    <div>
                        <label class="block text-sm text-gray-300 mb-2">转账用途</label>
                        <select id="transfer-purpose" class="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white">
                            <option value="">请选择转账用途</option>
                            <option value="生活费">生活费</option>
                            <option value="还款">还款</option>
                            <option value="投资理财">投资理财</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button onclick="window.bankingAIApp?.modules?.sameBankTransfer?.submitTransferInfo()"
                                class="flex-1 px-4 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium">
                            下一步
                        </button>
                        <button onclick="window.bankingAIApp?.modules?.sameBankTransfer?.handleUserInput('restart-transfer')"
                                class="px-4 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg text-white">
                            重新填写
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 提交转账信息（供HTML调用）
     */
    submitTransferInfo() {
        const payeeAccount = document.getElementById('payee-account')?.value;
        const amount = parseFloat(document.getElementById('transfer-amount')?.value);
        const purpose = document.getElementById('transfer-purpose')?.value;
        const payeeName = document.getElementById('payee-name')?.value;

        // 使用智能验证系统进行验证
        const payeeValidation = smartValidationSystem.validatePayeeInfo(payeeAccount, payeeName);
        const amountValidation = smartValidationSystem.validateTransferAmount(amount, {
            fromAccount: this.transferData.fromAccount
        });

        // 检查验证结果
        if (!payeeValidation.isValid) {
            const errorResult = payeeValidation.results.find(r => r.type === ValidationResult.ERROR);
            this._showError(errorResult.message);
            return;
        }

        if (!amountValidation.isValid) {
            const errorResult = amountValidation.results.find(r => r.type === ValidationResult.ERROR);
            this._showError(errorResult.message);
            return;
        }

        // 检查余额
        if (amount > this.transferData.fromAccount.balance) {
            this._showError('转账金额超过可用余额');
            return;
        }

        // 显示验证警告（如果有）
        const warnings = [
            ...payeeValidation.results.filter(r => r.type === ValidationResult.WARNING),
            ...amountValidation.results.filter(r => r.type === ValidationResult.WARNING)
        ];

        if (warnings.length > 0) {
            this._showValidationWarnings(warnings);
        }

        // 显示验证建议（如果有）
        const suggestions = [
            ...payeeValidation.results.filter(r => r.type === ValidationResult.SUGGESTION),
            ...amountValidation.results.filter(r => r.type === ValidationResult.SUGGESTION)
        ];

        if (suggestions.length > 0) {
            this._showValidationSuggestions(suggestions);
        }

        this.handleUserInput('submit-transfer-info', {
            toAccount: payeeAccount,
            toAccountName: payeeName,
            amount: amount,
            purpose: purpose || '其他',
            validationResults: {
                payee: payeeValidation,
                amount: amountValidation
            }
        });
    }
    
    /**
     * 显示收款人验证过程
     * @private
     */
    _displayPayeeVerification() {
        const content = `
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-yellow-400">🔍</span>
                    <span class="font-semibold text-yellow-400">正在验证收款人信息</span>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <div class="animate-spin w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full"></div>
                        <span class="text-sm text-gray-300">验证账号：${this.transferData.toAccount}</span>
                    </div>
                    <div class="text-xs text-gray-400">正在查询收款人姓名和账户状态...</div>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示收款人验证结果
     * @private
     */
    _displayPayeeVerificationResult() {
        // 模拟收款人信息
        const payeeInfo = this._generatePayeeInfo(this.transferData.toAccount);
        
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-green-400">✅</span>
                    <span class="font-semibold text-green-400">收款人验证成功</span>
                </div>
                <div class="bg-gray-800/50 rounded-lg p-3 mb-4">
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">收款人姓名：</span>
                            <span class="text-gray-200">${payeeInfo.name}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">收款账号：</span>
                            <span class="text-gray-200">${this.transferData.toAccount}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">开户行：</span>
                            <span class="text-gray-200">${payeeInfo.bankBranch}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">账户状态：</span>
                            <span class="text-green-400">正常</span>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <button onclick="window.bankingAIApp?.modules?.sameBankTransfer?.handleUserInput('confirm-payee')"
                            class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-white">
                        确认收款人信息
                    </button>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 获取默认付款账户
     * @private
     */
    _getDefaultFromAccount() {
        return {
            cardNumber: '8899',
            cardType: '工资卡',
            balance: 125680.50,
            accountName: '张先生'
        };
    }
    
    /**
     * 生成交易ID
     * @private
     */
    _generateTransactionId() {
        const timestamp = Date.now().toString();
        const random = Math.random().toString(36).substr(2, 6).toUpperCase();
        return `TXN${timestamp.slice(-8)}${random}`;
    }
    
    /**
     * 生成收款人信息
     * @private
     */
    _generatePayeeInfo(accountNumber) {
        const names = ['李女士', '王先生', '陈女士', '刘先生', '赵女士'];
        const branches = ['总行营业部', '科技园支行', '市中心支行', '开发区支行', '大学城支行'];
        
        return {
            name: names[Math.floor(Math.random() * names.length)],
            bankBranch: branches[Math.floor(Math.random() * branches.length)]
        };
    }
    
    /**
     * 显示错误消息
     * @private
     */
    _showError(message) {
        const content = `
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-red-400">⚠️</span>
                    <span class="text-sm text-red-400">${message}</span>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 添加消息到界面
     * @private
     */
    _addMessage(content) {
        // 触发消息事件，由主应用处理显示
        eventBus.emit('ui:add-ai-message', { content });
    }
    
    /**
     * 处理错误
     * @private
     */
    _handleError(error) {
        console.error('[SameBankTransferFlow] Error:', error);
        
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_ERROR, {
            service: 'same-bank-transfer',
            error: error.message
        });
    }
    
    /**
     * 显示金额确认界面
     * @private
     */
    _displayAmountConfirmation() {
        const content = `
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-purple-400">💰</span>
                    <span class="font-semibold text-purple-400">转账金额确认</span>
                </div>
                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">¥${this.transferData.amount.toLocaleString()}</div>
                        <div class="text-sm text-gray-400">转账金额</div>
                    </div>
                </div>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">付款账户：</span>
                        <span class="text-gray-200">尾号${this.transferData.fromAccount.cardNumber}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">收款账户：</span>
                        <span class="text-gray-200">${this.transferData.toAccount}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">转账用途：</span>
                        <span class="text-gray-200">${this.transferData.purpose}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">手续费：</span>
                        <span class="text-green-400">免费</span>
                    </div>
                    <div class="flex justify-between border-t border-gray-600 pt-2">
                        <span class="text-gray-400">实际到账：</span>
                        <span class="text-white font-medium">¥${this.transferData.amount.toLocaleString()}</span>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button onclick="window.bankingAIApp?.modules?.sameBankTransfer?.handleUserInput('confirm-amount')"
                            class="px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg text-white font-medium">
                        确认转账
                    </button>
                </div>
            </div>
        `;

        this._addMessage(content);
    }

    /**
     * 显示安全验证界面
     * @private
     */
    _displaySecurityVerification() {
        const content = `
            <div class="bg-orange-900/20 border border-orange-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-orange-400">🔐</span>
                    <span class="font-semibold text-orange-400">安全验证</span>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">1</span>
                            </div>
                            <div>
                                <div class="font-medium text-gray-200">人脸识别验证</div>
                                <div class="text-sm text-gray-400">请正面面对摄像头</div>
                            </div>
                            <div class="ml-auto">
                                <div class="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">2</span>
                            </div>
                            <div>
                                <div class="font-medium text-gray-400">短信验证码</div>
                                <div class="text-sm text-gray-500">等待人脸识别完成</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-xs text-gray-500 text-center">
                    正在进行安全验证，请稍候...
                </div>
            </div>
        `;

        this._addMessage(content);
    }

    /**
     * 显示转账执行进度
     * @private
     */
    _displayTransferExecution() {
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-green-400">⚡</span>
                    <span class="font-semibold text-green-400">正在执行转账</span>
                </div>
                <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-400 mb-2">
                        <span>执行进度</span>
                        <span id="transfer-execution-percentage">0%</span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-2">
                        <div id="transfer-execution-progress-bar" class="bg-green-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                    </div>
                </div>
                <div id="transfer-execution-steps" class="space-y-3">
                    <!-- 执行步骤将在这里动态显示 -->
                </div>
                <div id="transfer-current-step-info" class="mt-4 p-3 bg-gray-800/50 rounded-lg">
                    <div class="text-sm text-gray-300">准备开始转账...</div>
                </div>
            </div>
        `;

        this._addMessage(content);
    }

    /**
     * 更新转账执行UI
     * @private
     */
    _updateTransferExecutionUI(step, currentIndex, totalSteps) {
        const percentage = Math.round(((currentIndex + 1) / totalSteps) * 100);

        // 更新进度条
        const progressBar = document.getElementById('transfer-execution-progress-bar');
        const percentageText = document.getElementById('transfer-execution-percentage');

        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
        if (percentageText) {
            percentageText.textContent = `${percentage}%`;
        }

        // 更新当前步骤信息
        const currentStepInfo = document.getElementById('transfer-current-step-info');
        if (currentStepInfo) {
            currentStepInfo.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="text-2xl">${step.icon}</div>
                    <div>
                        <div class="font-medium text-green-400">${step.name}</div>
                        <div class="text-sm text-gray-400">正在处理中...</div>
                    </div>
                    <div class="ml-auto">
                        <div class="animate-spin w-5 h-5 border-2 border-green-500 border-t-transparent rounded-full"></div>
                    </div>
                </div>
            `;
        }

        // 添加或更新步骤列表
        const stepsContainer = document.getElementById('transfer-execution-steps');
        if (stepsContainer) {
            let stepElement = stepsContainer.querySelector(`[data-step-index="${currentIndex}"]`);

            if (!stepElement) {
                stepElement = document.createElement('div');
                stepElement.setAttribute('data-step-index', currentIndex);
                stepElement.className = 'flex items-center space-x-3 p-2 rounded-lg bg-gray-800/30';
                stepsContainer.appendChild(stepElement);
            }

            stepElement.innerHTML = `
                <div class="text-lg">${step.icon}</div>
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-200">${step.name}</div>
                    <div class="text-xs text-gray-400">处理中...</div>
                </div>
                <div class="w-5 h-5 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
            `;
        }
    }

    /**
     * 标记转账步骤完成
     * @private
     */
    _markTransferStepCompleted(step, stepIndex) {
        const stepsContainer = document.getElementById('transfer-execution-steps');
        if (stepsContainer) {
            const stepElement = stepsContainer.querySelector(`[data-step-index="${stepIndex}"]`);
            if (stepElement) {
                stepElement.innerHTML = `
                    <div class="text-lg">${step.icon}</div>
                    <div class="flex-1">
                        <div class="text-sm font-medium text-gray-200">${step.name}</div>
                        <div class="text-xs text-gray-400">已完成</div>
                    </div>
                    <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-xs">✓</span>
                    </div>
                `;
                stepElement.className = 'flex items-center space-x-3 p-2 rounded-lg bg-green-900/20 border border-green-600/30';
            }
        }
    }

    /**
     * 显示转账成功消息
     * @private
     */
    _displayTransferSuccess() {
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-green-400 text-6xl mb-4">🎉</div>
                    <div class="text-xl font-bold text-green-400 mb-2">转账成功！</div>
                    <div class="text-sm text-gray-300 mb-4">
                        您的转账已成功处理，资金已实时到账
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">交易流水号：</span>
                                <span class="text-gray-200 font-mono">${this.transferData.transactionId}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">转账金额：</span>
                                <span class="text-green-400 font-medium">¥${this.transferData.amount.toLocaleString()}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">手续费：</span>
                                <span class="text-green-400">免费</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">完成时间：</span>
                                <span class="text-gray-200">${new Date().toLocaleString('zh-CN')}</span>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-3">
                        <button onclick="window.bankingAIApp?.modules?.sameBankTransfer?.downloadReceipt()"
                                class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm text-white">
                            下载凭证
                        </button>
                        <button onclick="window.bankingAIApp?.modules?.sameBankTransfer?.handleUserInput('restart-transfer')"
                                class="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm text-white">
                            再次转账
                        </button>
                    </div>
                </div>
            </div>
        `;

        this._addMessage(content);
    }

    /**
     * 下载转账凭证（供HTML调用）
     */
    downloadReceipt() {
        // 模拟下载凭证
        const receiptData = {
            transactionId: this.transferData.transactionId,
            amount: this.transferData.amount,
            fromAccount: this.transferData.fromAccount.cardNumber,
            toAccount: this.transferData.toAccount,
            purpose: this.transferData.purpose,
            timestamp: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(receiptData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `transfer-receipt-${this.transferData.transactionId}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this._showSuccess('转账凭证已下载');
    }

    /**
     * 显示成功消息
     * @private
     */
    _showSuccess(message) {
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm text-green-400">${message}</span>
                </div>
            </div>
        `;

        this._addMessage(content);
    }

    /**
     * 处理实时验证（供HTML调用）
     */
    handleRealTimeValidation(field, value) {
        const validation = smartValidationSystem.getRealTimeValidation(field, value, {
            fromAccount: this.transferData.fromAccount
        });

        this._updateValidationUI(field, validation);

        // 显示智能建议
        if (validation.suggestions && validation.suggestions.length > 0) {
            this._showSmartSuggestions(field, validation.suggestions);
        }
    }

    /**
     * 更新验证UI
     * @private
     */
    _updateValidationUI(field, validation) {
        let messageElementId;
        let messageClass;

        switch (field) {
            case 'accountNumber':
                messageElementId = 'account-validation-message';
                break;
            case 'accountName':
                messageElementId = 'name-validation-message';
                break;
            case 'amount':
                messageElementId = 'amount-validation-message';
                break;
            default:
                return;
        }

        const messageElement = document.getElementById(messageElementId);
        if (!messageElement) return;

        if (validation.message) {
            messageClass = validation.isValid ? 'text-green-400' : 'text-red-400';
            messageElement.textContent = validation.message;
            messageElement.className = `text-xs mt-1 ${messageClass}`;
        } else {
            messageElement.textContent = '';
            messageElement.className = 'text-xs mt-1';
        }
    }

    /**
     * 显示智能建议
     * @private
     */
    _showSmartSuggestions(field, suggestions) {
        let suggestionsElementId;

        switch (field) {
            case 'accountNumber':
                suggestionsElementId = 'account-suggestions';
                this._showAccountSuggestions(suggestions);
                break;
            case 'amount':
                suggestionsElementId = 'amount-suggestions';
                this._showAmountSuggestions(suggestions);
                break;
            default:
                return;
        }
    }

    /**
     * 显示账号建议
     * @private
     */
    _showAccountSuggestions(suggestions) {
        const container = document.getElementById('account-suggestions');
        if (!container) return;

        if (suggestions.length === 0) {
            container.classList.add('hidden');
            return;
        }

        container.innerHTML = suggestions.map(suggestion => `
            <div class="p-2 bg-gray-700 hover:bg-gray-600 rounded cursor-pointer text-sm"
                 onclick="window.bankingAIApp?.modules?.sameBankTransfer?.selectAccountSuggestion('${suggestion}')">
                ${suggestion}
            </div>
        `).join('');

        container.classList.remove('hidden');
    }

    /**
     * 显示金额建议
     * @private
     */
    _showAmountSuggestions(suggestions) {
        const container = document.getElementById('amount-suggestions');
        if (!container) return;

        if (suggestions.length === 0) {
            container.classList.add('hidden');
            return;
        }

        // 获取智能金额建议
        const smartSuggestions = smartValidationSystem.getSmartAmountSuggestions();

        container.innerHTML = smartSuggestions.slice(0, 8).map(suggestion => `
            <button class="p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs text-center"
                    onclick="window.bankingAIApp?.modules?.sameBankTransfer?.selectAmountSuggestion(${suggestion.amount})">
                ${suggestion.displayText}
            </button>
        `).join('');

        container.classList.remove('hidden');
    }

    /**
     * 选择账号建议（供HTML调用）
     */
    selectAccountSuggestion(suggestion) {
        // 解析建议文本，提取账号
        const match = suggestion.match(/\(([^)]+)\)/);
        if (match) {
            const maskedAccount = match[1];
            // 这里应该从建议数据中获取完整账号，简化处理
            const recommendations = smartValidationSystem.getFrequentPayeeRecommendations();
            const recommendation = recommendations.find(r => r.displayText === suggestion);

            if (recommendation) {
                document.getElementById('payee-account').value = recommendation.accountNumber;
                document.getElementById('payee-name').value = recommendation.name;

                // 隐藏建议
                document.getElementById('account-suggestions').classList.add('hidden');

                // 触发验证
                this.handleRealTimeValidation('accountNumber', recommendation.accountNumber);
            }
        }
    }

    /**
     * 选择金额建议（供HTML调用）
     */
    selectAmountSuggestion(amount) {
        document.getElementById('transfer-amount').value = amount;

        // 隐藏建议
        document.getElementById('amount-suggestions').classList.add('hidden');

        // 触发验证
        this.handleRealTimeValidation('amount', amount);
    }

    /**
     * 显示验证警告
     * @private
     */
    _showValidationWarnings(warnings) {
        const warningMessages = warnings.map(w => w.message).join('；');
        const content = `
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-yellow-400">⚠️</span>
                    <span class="text-sm text-yellow-400">验证警告：${warningMessages}</span>
                </div>
            </div>
        `;

        this._addMessage(content);
    }

    /**
     * 显示验证建议
     * @private
     */
    _showValidationSuggestions(suggestions) {
        const suggestionMessages = suggestions.map(s => s.message).join('；');
        const content = `
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-blue-400">💡</span>
                    <span class="text-sm text-blue-400">智能提示：${suggestionMessages}</span>
                </div>
            </div>
        `;

        this._addMessage(content);
    }

    /**
     * 延迟函数
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听演示重置事件
        eventBus.on(DemoEvents.DEMO_RESET, () => {
            this.stop();
        });
        
        // 监听UI消息事件
        eventBus.on('ui:add-ai-message', (event) => {
            if (window.bankingAIApp && window.bankingAIApp.addAIMessage) {
                window.bankingAIApp.addAIMessage(event.data.content);
            }
        });
    }
}

// 创建全局实例
export const sameBankTransferFlow = new SameBankTransferFlow();

// 导出类
export default SameBankTransferFlow;

/**
 * 银信通签约流程模块
 * 
 * 负责处理银信通签约的完整业务流程，
 * 包括需求分析、方案生成、用户确认和执行流程
 */

import { eventBus, DemoEvents } from '../../../core/event-bus.js';
import { stateManager } from '../../../core/state-manager.js';
import { thinkingChain } from '../../ai-engine/thinking-chain.js';

/**
 * 银信通签约流程类
 */
class YinxintongSignupFlow {
    constructor() {
        // 流程状态
        this.currentStep = null;
        this.isActive = false;
        
        // 流程步骤定义
        this.steps = [
            { id: 'need-analysis', name: '需求分析', handler: this._handleNeedAnalysis.bind(this) },
            { id: 'solution-generation', name: '方案生成', handler: this._handleSolutionGeneration.bind(this) },
            { id: 'user-confirmation', name: '用户确认', handler: this._handleUserConfirmation.bind(this) },
            { id: 'execution', name: '执行流程', handler: this._handleExecution.bind(this) },
            { id: 'completion', name: '完成反馈', handler: this._handleCompletion.bind(this) }
        ];
        
        // 用户数据
        this.userData = null;
        this.selectedSolution = null;
        
        this._setupEventListeners();
    }
    
    /**
     * 开始银信通签约流程
     * @param {Object} context - 业务上下文
     */
    async start(context = {}) {
        if (this.isActive) {
            console.warn('[YinxintongSignupFlow] Flow already active');
            return;
        }
        
        this.isActive = true;
        this.userData = context.userData || this._getDefaultUserData();
        
        // 更新业务状态
        stateManager.updateState({
            'business.currentService': 'yinxintong-signup',
            'business.serviceData': {
                userData: this.userData,
                startTime: Date.now()
            }
        });
        
        console.log('[YinxintongSignupFlow] Started');
        
        // 开始第一步
        await this._executeStep('need-analysis');
    }
    
    /**
     * 停止流程
     */
    stop() {
        if (!this.isActive) {
            return;
        }
        
        this.isActive = false;
        this.currentStep = null;
        
        // 清理状态
        stateManager.updateState('business.currentService', null);
        
        console.log('[YinxintongSignupFlow] Stopped');
    }
    
    /**
     * 跳转到指定步骤
     * @param {string} stepId - 步骤ID
     */
    async jumpToStep(stepId) {
        const step = this.steps.find(s => s.id === stepId);
        if (!step) {
            console.error('[YinxintongSignupFlow] Invalid step:', stepId);
            return;
        }
        
        await this._executeStep(stepId);
    }
    
    /**
     * 处理用户选择
     * @param {string} action - 用户动作
     * @param {*} data - 相关数据
     */
    handleUserAction(action, data = null) {
        console.log('[YinxintongSignupFlow] User action:', action, data);
        
        switch (action) {
            case 'confirm-solution':
                this.selectedSolution = data;
                this._executeStep('execution');
                break;
                
            case 'customize-solution':
                this._handleSolutionCustomization(data);
                break;
                
            case 'restart-flow':
                this.stop();
                this.start();
                break;
                
            default:
                console.warn('[YinxintongSignupFlow] Unknown action:', action);
        }
    }
    
    /**
     * 执行指定步骤
     * @private
     */
    async _executeStep(stepId) {
        const step = this.steps.find(s => s.id === stepId);
        if (!step) {
            console.error('[YinxintongSignupFlow] Step not found:', stepId);
            return;
        }
        
        this.currentStep = stepId;
        
        // 触发步骤开始事件
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_STEP, {
            service: 'yinxintong-signup',
            stepId,
            stepName: step.name
        });
        
        console.log(`[YinxintongSignupFlow] Executing step: ${stepId}`);
        
        try {
            await step.handler();
        } catch (error) {
            console.error(`[YinxintongSignupFlow] Error in step ${stepId}:`, error);
            this._handleError(error);
        }
    }
    
    /**
     * 处理需求分析步骤
     * @private
     */
    async _handleNeedAnalysis() {
        // 显示AI思维过程
        const thinkingSteps = thinkingChain.generateYinxintongSteps('signup', {
            userData: this.userData
        });
        
        await thinkingChain.start(thinkingSteps);
        
        // 分析用户需求
        const analysis = this._analyzeUserNeeds();
        
        // 更新状态
        stateManager.updateState('business.serviceData.needsAnalysis', analysis);
        
        // 显示分析结果
        this._displayNeedsAnalysis(analysis);
        
        // 自动进入下一步
        setTimeout(() => {
            if (this.isActive) {
                this._executeStep('solution-generation');
            }
        }, 2000);
    }
    
    /**
     * 处理方案生成步骤
     * @private
     */
    async _handleSolutionGeneration() {
        // 生成推荐方案
        const solutions = this._generateSolutions();
        
        // 更新状态
        stateManager.updateState('business.recommendations', solutions);
        
        // 显示方案选择界面
        this._displaySolutionOptions(solutions);
        
        // 等待用户选择（不自动进入下一步）
    }
    
    /**
     * 处理用户确认步骤
     * @private
     */
    async _handleUserConfirmation() {
        if (!this.selectedSolution) {
            console.error('[YinxintongSignupFlow] No solution selected');
            return;
        }
        
        // 显示确认界面
        this._displayConfirmation();
    }
    
    /**
     * 处理执行流程步骤
     * @private
     */
    async _handleExecution() {
        // 显示执行进度
        this._displayExecutionProgress();
        
        // 模拟执行步骤
        const executionSteps = [
            { name: '身份验证', duration: 2000 },
            { name: '银行卡绑定', duration: 1500 },
            { name: '手机号确认', duration: 1000 },
            { name: '短信验证', duration: 2000 },
            { name: '通知设置', duration: 1500 },
            { name: '服务确认', duration: 1000 }
        ];
        
        for (let i = 0; i < executionSteps.length; i++) {
            const step = executionSteps[i];
            
            // 更新进度
            stateManager.updateState('business.executionProgress', {
                currentStep: i,
                totalSteps: executionSteps.length,
                currentStepName: step.name,
                isExecuting: true
            });
            
            // 等待步骤完成
            await this._delay(step.duration);
        }
        
        // 执行完成
        stateManager.updateState('business.executionProgress.isExecuting', false);
        
        // 进入完成步骤
        setTimeout(() => {
            if (this.isActive) {
                this._executeStep('completion');
            }
        }, 1000);
    }
    
    /**
     * 处理完成反馈步骤
     * @private
     */
    async _handleCompletion() {
        // 显示成功消息
        this._displaySuccessMessage();
        
        // 更新用户状态
        stateManager.updateState('user.profile.hasYinxintong', true);
        
        // 触发完成事件
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_COMPLETE, {
            service: 'yinxintong-signup',
            result: 'success',
            solution: this.selectedSolution,
            duration: Date.now() - stateManager.getState('business.serviceData.startTime')
        });
        
        // 停止流程
        this.stop();
    }
    
    /**
     * 分析用户需求
     * @private
     */
    _analyzeUserNeeds() {
        const userData = this.userData;
        
        return {
            cardType: userData.cardType,
            monthlyTransactions: userData.monthlyTransactions || 18,
            averageAmount: userData.averageAmount || 850,
            riskLevel: 'low',
            recommendedThreshold: userData.cardType === '工资卡' ? 100 : 200,
            confidence: 95
        };
    }
    
    /**
     * 生成解决方案
     * @private
     */
    _generateSolutions() {
        const analysis = stateManager.getState('business.serviceData.needsAnalysis');
        const threshold = analysis?.recommendedThreshold || 100;
        
        return [
            {
                id: 'smart-recommended',
                title: '🌟 AI智能推荐',
                description: `收入全额通知，支出≥${threshold}元通知`,
                price: '2元/月',
                recommended: true,
                confidence: 95,
                features: [
                    '工资到账即时提醒',
                    `过滤${threshold}元以下小额消费`,
                    '余额不足智能预警',
                    '异常交易实时监控'
                ],
                savings: `预计减少${Math.round(analysis?.monthlyTransactions * 0.6 || 10)}条无效通知/月`,
                aiReason: `基于您月均${analysis?.monthlyTransactions || 18}笔交易的分析结果`
            },
            {
                id: 'comprehensive',
                title: '📊 全面监控方案',
                description: '所有交易都通知，全面掌控',
                price: '2元/月',
                confidence: 70,
                features: [
                    '所有收入提醒',
                    '所有支出提醒',
                    '实时余额更新',
                    '交易分类统计'
                ],
                warnings: ['可能产生较多通知'],
                aiReason: '适合需要详细了解每笔交易的用户'
            },
            {
                id: 'custom',
                title: '⚙️ 自定义方案',
                description: '完全按您的需求定制',
                price: '2元/月',
                confidence: 0,
                features: [
                    '自定义金额阈值',
                    '选择通知时间段',
                    '个性化通知内容',
                    '多渠道通知选择'
                ],
                customizable: true,
                aiReason: '为有特殊需求的用户提供灵活配置'
            }
        ];
    }
    
    /**
     * 获取默认用户数据
     * @private
     */
    _getDefaultUserData() {
        return {
            name: '张先生',
            cardNumber: '8899',
            cardType: '工资卡',
            phoneNumber: '138****8888',
            monthlyTransactions: 18,
            averageAmount: 850,
            hasYinxintong: false
        };
    }
    
    /**
     * 显示需求分析结果
     * @private
     */
    _displayNeedsAnalysis(analysis) {
        const content = `
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-blue-400">🎯</span>
                    <span class="font-semibold text-blue-400">需求分析完成</span>
                </div>
                <div class="space-y-2 text-sm text-gray-300">
                    <div>• 账户类型：${analysis.cardType}</div>
                    <div>• 月均交易：${analysis.monthlyTransactions}笔</div>
                    <div>• 建议阈值：${analysis.recommendedThreshold}元</div>
                    <div>• 风险等级：${analysis.riskLevel === 'low' ? '低风险' : '中风险'}</div>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示方案选择界面
     * @private
     */
    _displaySolutionOptions(solutions) {
        const solutionCards = solutions.map(solution => `
            <div class="border border-gray-600 rounded-lg p-4 ${solution.recommended ? 'border-green-500 bg-green-900/10' : ''}">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-200">${solution.title}</h4>
                    ${solution.confidence > 0 ? `<span class="text-xs text-gray-400">${solution.confidence}%</span>` : ''}
                </div>
                <p class="text-sm text-gray-300 mb-3">${solution.description}</p>
                <div class="text-sm text-gray-400 mb-3">月费：${solution.price}</div>
                <div class="space-y-1 text-xs text-gray-400 mb-3">
                    ${solution.features.map(feature => `<div>• ${feature}</div>`).join('')}
                </div>
                <button onclick="window.bankingAIApp?.modules?.yinxintongSignup?.handleUserAction('confirm-solution', '${solution.id}')"
                        class="w-full px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                    选择此方案
                </button>
            </div>
        `).join('');
        
        const content = `
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-purple-400">💡</span>
                    <span class="font-semibold text-purple-400">为您推荐以下方案</span>
                </div>
                <div class="grid gap-4">
                    ${solutionCards}
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示确认界面
     * @private
     */
    _displayConfirmation() {
        const solution = this.selectedSolution;
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-green-400">✅</span>
                    <span class="font-semibold text-green-400">确认签约信息</span>
                </div>
                <p class="text-sm text-gray-300">您选择了：${solution.title}</p>
                <div class="mt-3 text-center">
                    <button onclick="window.bankingAIApp?.modules?.yinxintongSignup?.handleUserAction('confirm-solution', '${solution.id}')"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded">
                        确认办理
                    </button>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示执行进度
     * @private
     */
    _displayExecutionProgress() {
        const content = `
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-blue-400">⚡</span>
                    <span class="font-semibold text-blue-400">正在执行签约流程</span>
                </div>
                <div id="execution-progress" class="space-y-2">
                    <!-- 进度将动态更新 -->
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示成功消息
     * @private
     */
    _displaySuccessMessage() {
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-green-400 text-6xl mb-4">🎉</div>
                    <div class="text-xl font-bold text-green-400 mb-2">银信通签约成功！</div>
                    <div class="text-sm text-gray-300 mb-4">
                        恭喜您成功开通银信通短信提醒服务
                    </div>
                    <div class="text-xs text-gray-400">
                        您将在5分钟内收到服务开通确认短信
                    </div>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 添加消息到界面
     * @private
     */
    _addMessage(content) {
        // 触发消息事件，由主应用处理显示
        eventBus.emit('ui:add-ai-message', { content });
    }
    
    /**
     * 处理错误
     * @private
     */
    _handleError(error) {
        console.error('[YinxintongSignupFlow] Error:', error);
        
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_ERROR, {
            service: 'yinxintong-signup',
            error: error.message
        });
    }
    
    /**
     * 延迟函数
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听演示重置事件
        eventBus.on(DemoEvents.DEMO_RESET, () => {
            this.stop();
        });
        
        // 监听UI消息事件
        eventBus.on('ui:add-ai-message', (event) => {
            if (window.bankingAIApp && window.bankingAIApp.addAIMessage) {
                window.bankingAIApp.addAIMessage(event.data.content);
            }
        });
    }
}

// 创建全局实例
export const yinxintongSignupFlow = new YinxintongSignupFlow();

// 导出类
export default YinxintongSignupFlow;

/**
 * 密码重置业务模块
 * 
 * 负责处理银行卡密码重置的完整业务流程，
 * 包括身份验证、安全检查、密码设置和确认
 */

import { eventBus, DemoEvents } from '../../../core/event-bus.js';
import { stateManager } from '../../../core/state-manager.js';
import { thinkingChain } from '../../ai-engine/thinking-chain.js';

/**
 * 密码重置流程类
 */
class PasswordResetFlow {
    constructor() {
        // 流程状态
        this.currentStep = null;
        this.isActive = false;
        
        // 流程步骤定义
        this.steps = [
            { id: 'identity-verification', name: '身份验证', handler: this._handleIdentityVerification.bind(this) },
            { id: 'security-questions', name: '安全问题验证', handler: this._handleSecurityQuestions.bind(this) },
            { id: 'new-password-setting', name: '新密码设置', handler: this._handleNewPasswordSetting.bind(this) },
            { id: 'password-confirmation', name: '密码确认', handler: this._handlePasswordConfirmation.bind(this) },
            { id: 'completion', name: '重置完成', handler: this._handleCompletion.bind(this) }
        ];
        
        // 重置数据
        this.resetData = null;
        
        this._setupEventListeners();
    }
    
    /**
     * 开始密码重置流程
     * @param {Object} context - 业务上下文
     */
    async start(context = {}) {
        if (this.isActive) {
            console.warn('[PasswordResetFlow] Flow already active');
            return;
        }
        
        this.isActive = true;
        this.resetData = {
            cardInfo: context.cardInfo || this._getDefaultCardInfo(),
            verificationMethods: [],
            securityAnswers: {},
            newPassword: null,
            startTime: Date.now(),
            resetId: this._generateResetId()
        };
        
        // 更新业务状态
        stateManager.updateState({
            'business.currentService': 'password-reset',
            'business.serviceData': {
                resetData: this.resetData,
                startTime: Date.now()
            }
        });
        
        console.log('[PasswordResetFlow] Started');
        
        // 开始第一步
        await this._executeStep('identity-verification');
    }
    
    /**
     * 停止流程
     */
    stop() {
        if (!this.isActive) {
            return;
        }
        
        this.isActive = false;
        this.currentStep = null;
        
        // 清理状态
        stateManager.updateState('business.currentService', null);
        
        console.log('[PasswordResetFlow] Stopped');
    }
    
    /**
     * 处理用户输入
     * @param {string} action - 用户动作
     * @param {*} data - 相关数据
     */
    handleUserInput(action, data = null) {
        console.log('[PasswordResetFlow] User input:', action, data);
        
        switch (action) {
            case 'identity-verified':
                this._executeStep('security-questions');
                break;
                
            case 'security-answers-submitted':
                this.resetData.securityAnswers = data;
                this._executeStep('new-password-setting');
                break;
                
            case 'new-password-set':
                this.resetData.newPassword = data.password;
                this._executeStep('password-confirmation');
                break;
                
            case 'password-confirmed':
                this._executeStep('completion');
                break;
                
            case 'restart-reset':
                this.stop();
                this.start();
                break;
                
            default:
                console.warn('[PasswordResetFlow] Unknown action:', action);
        }
    }
    
    /**
     * 执行指定步骤
     * @private
     */
    async _executeStep(stepId) {
        const step = this.steps.find(s => s.id === stepId);
        if (!step) {
            console.error('[PasswordResetFlow] Step not found:', stepId);
            return;
        }
        
        this.currentStep = stepId;
        
        // 触发步骤开始事件
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_STEP, {
            service: 'password-reset',
            stepId,
            stepName: step.name
        });
        
        console.log(`[PasswordResetFlow] Executing step: ${stepId}`);
        
        try {
            await step.handler();
        } catch (error) {
            console.error(`[PasswordResetFlow] Error in step ${stepId}:`, error);
            this._handleError(error);
        }
    }
    
    /**
     * 处理身份验证步骤
     * @private
     */
    async _handleIdentityVerification() {
        // 显示AI思维过程
        const thinkingSteps = [
            { type: 'detection', text: '检测到密码重置需求', confidence: 98 },
            { type: 'analysis', text: '分析用户身份信息', confidence: 95 },
            { type: 'evaluation', text: '评估安全风险等级', confidence: 92 },
            { type: 'preparation', text: '准备多重验证流程', confidence: 100 }
        ];
        
        await thinkingChain.start(thinkingSteps);
        
        // 显示身份验证界面
        this._displayIdentityVerification();
    }
    
    /**
     * 处理安全问题验证步骤
     * @private
     */
    async _handleSecurityQuestions() {
        // 显示安全问题界面
        this._displaySecurityQuestions();
    }
    
    /**
     * 处理新密码设置步骤
     * @private
     */
    async _handleNewPasswordSetting() {
        // 显示新密码设置界面
        this._displayNewPasswordSetting();
    }
    
    /**
     * 处理密码确认步骤
     * @private
     */
    async _handlePasswordConfirmation() {
        // 显示密码确认界面
        this._displayPasswordConfirmation();
        
        // 模拟密码设置过程
        await this._delay(2000);
        
        // 自动进入完成步骤
        this.handleUserInput('password-confirmed');
    }
    
    /**
     * 处理完成步骤
     * @private
     */
    async _handleCompletion() {
        // 显示重置成功消息
        this._displayResetSuccess();
        
        // 触发完成事件
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_COMPLETE, {
            service: 'password-reset',
            result: 'success',
            resetData: this.resetData,
            duration: Date.now() - this.resetData.startTime
        });
        
        // 停止流程
        this.stop();
    }
    
    /**
     * 显示身份验证界面
     * @private
     */
    _displayIdentityVerification() {
        const content = `
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-red-400">🔐</span>
                    <span class="font-semibold text-red-400">密码重置 - 身份验证</span>
                </div>
                
                <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3 mb-4">
                    <div class="flex items-center space-x-2">
                        <span class="text-yellow-400">⚠️</span>
                        <span class="text-sm text-yellow-400">为了您的账户安全，需要进行多重身份验证</span>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm">1</span>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-200">银行卡验证</div>
                                    <div class="text-sm text-gray-400">验证银行卡信息</div>
                                </div>
                            </div>
                            <div class="text-green-400">✓</div>
                        </div>
                        <div class="ml-11 text-sm text-gray-400">
                            卡号：****${this.resetData.cardInfo.cardNumber} (${this.resetData.cardInfo.cardType})
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm">2</span>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-200">人脸识别验证</div>
                                    <div class="text-sm text-gray-400">请正面面对摄像头</div>
                                </div>
                            </div>
                            <div class="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm">3</span>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-400">手机短信验证</div>
                                    <div class="text-sm text-gray-500">等待前序验证完成</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <button onclick="window.bankingAIApp?.modules?.passwordReset?.simulateIdentityVerification()"
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white">
                        模拟验证完成
                    </button>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 模拟身份验证完成（供HTML调用）
     */
    async simulateIdentityVerification() {
        // 模拟验证过程
        this._showProgress('正在进行人脸识别验证...');
        await this._delay(2000);
        
        this._showProgress('正在发送短信验证码...');
        await this._delay(1500);
        
        this._showProgress('验证码已发送，请查收');
        await this._delay(1000);
        
        this._showSuccess('身份验证成功！');
        
        // 进入下一步
        setTimeout(() => {
            this.handleUserInput('identity-verified');
        }, 1000);
    }
    
    /**
     * 显示安全问题界面
     * @private
     */
    _displaySecurityQuestions() {
        const questions = this._getSecurityQuestions();
        
        const questionItems = questions.map((q, index) => `
            <div class="bg-gray-800/50 rounded-lg p-3">
                <div class="font-medium text-gray-200 mb-2">${q.question}</div>
                <input type="text" id="security-answer-${index}" 
                       class="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400"
                       placeholder="请输入答案">
            </div>
        `).join('');
        
        const content = `
            <div class="bg-orange-900/20 border border-orange-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-orange-400">❓</span>
                    <span class="font-semibold text-orange-400">安全问题验证</span>
                </div>
                
                <div class="mb-4 text-sm text-gray-300">
                    请回答以下安全问题以验证您的身份：
                </div>
                
                <div class="space-y-3 mb-4">
                    ${questionItems}
                </div>
                
                <div class="text-center">
                    <button onclick="window.bankingAIApp?.modules?.passwordReset?.submitSecurityAnswers()"
                            class="px-6 py-2 bg-orange-600 hover:bg-orange-700 rounded-lg text-white">
                        提交答案
                    </button>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 提交安全问题答案（供HTML调用）
     */
    submitSecurityAnswers() {
        const answers = {};
        const questions = this._getSecurityQuestions();
        
        for (let i = 0; i < questions.length; i++) {
            const input = document.getElementById(`security-answer-${i}`);
            if (input) {
                answers[questions[i].id] = input.value;
            }
        }
        
        // 验证答案（演示模式，简单验证）
        const allAnswered = Object.values(answers).every(answer => answer.trim().length > 0);
        
        if (!allAnswered) {
            this._showError('请回答所有安全问题');
            return;
        }
        
        this._showSuccess('安全问题验证通过！');
        
        setTimeout(() => {
            this.handleUserInput('security-answers-submitted', answers);
        }, 1000);
    }
    
    /**
     * 显示新密码设置界面
     * @private
     */
    _displayNewPasswordSetting() {
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-green-400">🔑</span>
                    <span class="font-semibold text-green-400">设置新密码</span>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-300 mb-2">新密码</label>
                        <input type="password" id="new-password" 
                               class="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                               placeholder="请输入6位数字密码" maxlength="6">
                        <div class="text-xs text-gray-500 mt-1">密码必须为6位数字</div>
                    </div>
                    
                    <div>
                        <label class="block text-sm text-gray-300 mb-2">确认新密码</label>
                        <input type="password" id="confirm-password" 
                               class="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400"
                               placeholder="请再次输入密码" maxlength="6">
                    </div>
                    
                    <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                        <div class="text-sm text-blue-400 mb-2">密码安全提示：</div>
                        <ul class="text-xs text-gray-400 space-y-1">
                            <li>• 请设置6位数字密码</li>
                            <li>• 避免使用生日、电话号码等易猜测的数字</li>
                            <li>• 建议定期更换密码</li>
                            <li>• 请妥善保管您的密码</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <button onclick="window.bankingAIApp?.modules?.passwordReset?.submitNewPassword()"
                            class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-white">
                        设置密码
                    </button>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 提交新密码（供HTML调用）
     */
    submitNewPassword() {
        const newPassword = document.getElementById('new-password')?.value;
        const confirmPassword = document.getElementById('confirm-password')?.value;
        
        if (!newPassword || !confirmPassword) {
            this._showError('请输入密码');
            return;
        }
        
        if (newPassword.length !== 6 || !/^\d{6}$/.test(newPassword)) {
            this._showError('密码必须为6位数字');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            this._showError('两次输入的密码不一致');
            return;
        }
        
        this._showSuccess('密码格式正确，正在设置...');
        
        setTimeout(() => {
            this.handleUserInput('new-password-set', { password: newPassword });
        }, 1000);
    }
    
    /**
     * 显示密码确认界面
     * @private
     */
    _displayPasswordConfirmation() {
        const content = `
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-purple-400">⚡</span>
                    <span class="font-semibold text-purple-400">正在设置新密码</span>
                </div>
                
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full"></div>
                        <span class="text-sm text-gray-300">正在加密新密码...</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full"></div>
                        <span class="text-sm text-gray-300">正在更新密码数据库...</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full"></div>
                        <span class="text-sm text-gray-300">正在同步安全设置...</span>
                    </div>
                </div>
                
                <div class="mt-4 text-xs text-gray-500 text-center">
                    请稍候，正在安全地设置您的新密码...
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示重置成功消息
     * @private
     */
    _displayResetSuccess() {
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-green-400 text-6xl mb-4">🎉</div>
                    <div class="text-xl font-bold text-green-400 mb-2">密码重置成功！</div>
                    <div class="text-sm text-gray-300 mb-4">
                        您的银行卡密码已成功重置
                    </div>
                    
                    <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">重置流水号：</span>
                                <span class="text-gray-200 font-mono">${this.resetData.resetId}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">银行卡号：</span>
                                <span class="text-gray-200">****${this.resetData.cardInfo.cardNumber}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">完成时间：</span>
                                <span class="text-gray-200">${new Date().toLocaleString('zh-CN')}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3 mb-4">
                        <div class="text-sm text-yellow-400">
                            <div class="font-medium mb-1">重要提醒：</div>
                            <div class="text-xs text-gray-400">
                                • 请妥善保管您的新密码<br>
                                • 建议定期更换密码<br>
                                • 如有疑问请联系客服：95588
                            </div>
                        </div>
                    </div>
                    
                    <button onclick="window.bankingAIApp?.modules?.passwordReset?.handleUserInput('restart-reset')"
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white">
                        完成
                    </button>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 获取默认卡片信息
     * @private
     */
    _getDefaultCardInfo() {
        return {
            cardNumber: '8899',
            cardType: '工资卡',
            accountName: '张先生'
        };
    }
    
    /**
     * 生成重置ID
     * @private
     */
    _generateResetId() {
        const timestamp = Date.now().toString();
        const random = Math.random().toString(36).substr(2, 6).toUpperCase();
        return `RST${timestamp.slice(-8)}${random}`;
    }
    
    /**
     * 获取安全问题
     * @private
     */
    _getSecurityQuestions() {
        return [
            { id: 'mother_name', question: '您母亲的姓名是？' },
            { id: 'first_school', question: '您的小学校名是？' },
            { id: 'pet_name', question: '您第一只宠物的名字是？' }
        ];
    }
    
    /**
     * 显示进度消息
     * @private
     */
    _showProgress(message) {
        const content = `
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <div class="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                    <span class="text-sm text-blue-400">${message}</span>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示成功消息
     * @private
     */
    _showSuccess(message) {
        const content = `
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm text-green-400">${message}</span>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 显示错误消息
     * @private
     */
    _showError(message) {
        const content = `
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-red-400">⚠️</span>
                    <span class="text-sm text-red-400">${message}</span>
                </div>
            </div>
        `;
        
        this._addMessage(content);
    }
    
    /**
     * 添加消息到界面
     * @private
     */
    _addMessage(content) {
        // 触发消息事件，由主应用处理显示
        eventBus.emit('ui:add-ai-message', { content });
    }
    
    /**
     * 处理错误
     * @private
     */
    _handleError(error) {
        console.error('[PasswordResetFlow] Error:', error);
        
        eventBus.emit(DemoEvents.BUSINESS_PROCESS_ERROR, {
            service: 'password-reset',
            error: error.message
        });
    }
    
    /**
     * 延迟函数
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听演示重置事件
        eventBus.on(DemoEvents.DEMO_RESET, () => {
            this.stop();
        });
        
        // 监听UI消息事件
        eventBus.on('ui:add-ai-message', (event) => {
            if (window.bankingAIApp && window.bankingAIApp.addAIMessage) {
                window.bankingAIApp.addAIMessage(event.data.content);
            }
        });
    }
}

// 创建全局实例
export const passwordResetFlow = new PasswordResetFlow();

// 导出类
export default PasswordResetFlow;

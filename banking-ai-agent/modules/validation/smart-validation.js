/**
 * 智能防错与验证机制模块
 * 
 * 负责收款人信息校验、金额合理性检测、常用收款人推荐等功能，
 * 通过AI算法提供智能化的错误预防和用户体验优化
 */

import { eventBus, DemoEvents } from '../../core/event-bus.js';
import { stateManager } from '../../core/state-manager.js';

/**
 * 验证结果类型枚举
 */
export const ValidationResult = {
    VALID: 'valid',
    WARNING: 'warning',
    ERROR: 'error',
    SUGGESTION: 'suggestion'
};

/**
 * 智能验证系统类
 */
class SmartValidationSystem {
    constructor() {
        // 验证配置
        this.config = {
            // 金额验证配置
            amount: {
                minAmount: 0.01,
                maxSingleAmount: 500000,
                maxDailyAmount: 1000000,
                warningThreshold: 50000,
                suspiciousThreshold: 100000
            },
            
            // 收款人验证配置
            payee: {
                accountNumberLength: [16, 19],
                nameMinLength: 2,
                nameMaxLength: 20,
                blacklistKeywords: ['测试', 'test', '假', '虚拟']
            },
            
            // 行为分析配置
            behavior: {
                maxFrequency: 10, // 每小时最大交易次数
                suspiciousPatterns: ['round_numbers', 'frequent_same_amount', 'unusual_time']
            }
        };
        
        // 常用收款人数据库（模拟）
        this.frequentPayees = new Map([
            ['6222021234567890123', { name: '李女士', frequency: 15, lastUsed: Date.now() - ******** }],
            ['6222021234567890456', { name: '王先生', frequency: 8, lastUsed: Date.now() - ********* }],
            ['6222021234567890789', { name: '陈女士', frequency: 12, lastUsed: Date.now() - ********* }]
        ]);
        
        // 用户交易历史（模拟）
        this.transactionHistory = [];
        
        // 黑名单账户（模拟）
        this.blacklistedAccounts = new Set([
            '6222021234567890000',
            '6222021234567890001'
        ]);
        
        this._setupEventListeners();
    }
    
    /**
     * 验证收款人信息
     * @param {string} accountNumber - 收款人账号
     * @param {string} accountName - 收款人姓名
     * @param {Object} context - 验证上下文
     * @returns {Object} 验证结果
     */
    validatePayeeInfo(accountNumber, accountName = '', context = {}) {
        const results = [];
        
        // 账号格式验证
        const accountValidation = this._validateAccountNumber(accountNumber);
        results.push(accountValidation);
        
        // 姓名验证
        if (accountName) {
            const nameValidation = this._validateAccountName(accountName);
            results.push(nameValidation);
        }
        
        // 黑名单检查
        const blacklistValidation = this._checkBlacklist(accountNumber);
        results.push(blacklistValidation);
        
        // 常用收款人检查
        const frequentPayeeCheck = this._checkFrequentPayee(accountNumber);
        if (frequentPayeeCheck) {
            results.push(frequentPayeeCheck);
        }
        
        // 账号姓名匹配验证（模拟银行接口）
        if (accountName) {
            const matchValidation = this._validateAccountNameMatch(accountNumber, accountName);
            results.push(matchValidation);
        }
        
        return {
            isValid: !results.some(r => r.type === ValidationResult.ERROR),
            results: results,
            suggestions: this._generatePayeeSuggestions(accountNumber, results)
        };
    }
    
    /**
     * 验证转账金额
     * @param {number} amount - 转账金额
     * @param {Object} context - 验证上下文
     * @returns {Object} 验证结果
     */
    validateTransferAmount(amount, context = {}) {
        const results = [];
        
        // 基础金额验证
        const basicValidation = this._validateBasicAmount(amount);
        results.push(basicValidation);
        
        // 限额检查
        const limitValidation = this._validateAmountLimits(amount, context);
        results.push(limitValidation);
        
        // 异常金额检测
        const anomalyValidation = this._detectAmountAnomaly(amount, context);
        if (anomalyValidation) {
            results.push(anomalyValidation);
        }
        
        // 用户行为分析
        const behaviorValidation = this._analyzeBehaviorPattern(amount, context);
        if (behaviorValidation) {
            results.push(behaviorValidation);
        }
        
        return {
            isValid: !results.some(r => r.type === ValidationResult.ERROR),
            results: results,
            suggestions: this._generateAmountSuggestions(amount, results)
        };
    }
    
    /**
     * 获取常用收款人推荐
     * @param {string} query - 查询字符串
     * @param {number} limit - 返回数量限制
     * @returns {Array} 推荐列表
     */
    getFrequentPayeeRecommendations(query = '', limit = 5) {
        const recommendations = [];
        
        // 转换为数组并排序
        const payeeArray = Array.from(this.frequentPayees.entries())
            .map(([account, info]) => ({
                accountNumber: account,
                name: info.name,
                frequency: info.frequency,
                lastUsed: info.lastUsed,
                score: this._calculateRecommendationScore(info)
            }))
            .sort((a, b) => b.score - a.score);
        
        // 过滤和匹配
        for (const payee of payeeArray) {
            if (recommendations.length >= limit) break;
            
            // 如果有查询条件，进行匹配
            if (query) {
                const queryLower = query.toLowerCase();
                const matchesAccount = payee.accountNumber.includes(query);
                const matchesName = payee.name.toLowerCase().includes(queryLower);
                
                if (!matchesAccount && !matchesName) {
                    continue;
                }
            }
            
            recommendations.push({
                accountNumber: payee.accountNumber,
                name: payee.name,
                displayText: `${payee.name} (${this._maskAccountNumber(payee.accountNumber)})`,
                frequency: payee.frequency,
                lastUsed: payee.lastUsed,
                confidence: Math.min(payee.score / 20 * 100, 100)
            });
        }
        
        return recommendations;
    }
    
    /**
     * 智能金额建议
     * @param {Object} context - 上下文信息
     * @returns {Array} 金额建议列表
     */
    getSmartAmountSuggestions(context = {}) {
        const suggestions = [];
        
        // 基于历史交易的建议
        const historySuggestions = this._getHistoryBasedAmountSuggestions();
        suggestions.push(...historySuggestions);
        
        // 常用金额建议
        const commonAmounts = [100, 200, 500, 1000, 2000, 5000, 10000];
        for (const amount of commonAmounts) {
            suggestions.push({
                amount: amount,
                displayText: `¥${amount.toLocaleString()}`,
                reason: '常用金额',
                confidence: 70
            });
        }
        
        // 去重并排序
        const uniqueSuggestions = suggestions
            .filter((suggestion, index, self) => 
                index === self.findIndex(s => s.amount === suggestion.amount))
            .sort((a, b) => b.confidence - a.confidence)
            .slice(0, 8);
        
        return uniqueSuggestions;
    }
    
    /**
     * 实时验证反馈
     * @param {string} field - 字段名
     * @param {*} value - 字段值
     * @param {Object} context - 上下文
     * @returns {Object} 实时验证结果
     */
    getRealTimeValidation(field, value, context = {}) {
        switch (field) {
            case 'accountNumber':
                return this._getRealTimeAccountValidation(value);
            case 'amount':
                return this._getRealTimeAmountValidation(value, context);
            case 'accountName':
                return this._getRealTimeNameValidation(value);
            default:
                return { isValid: true, message: '', suggestions: [] };
        }
    }
    
    /**
     * 验证账号格式
     * @private
     */
    _validateAccountNumber(accountNumber) {
        if (!accountNumber || typeof accountNumber !== 'string') {
            return {
                type: ValidationResult.ERROR,
                field: 'accountNumber',
                message: '请输入收款人账号',
                code: 'ACCOUNT_REQUIRED'
            };
        }
        
        // 移除空格和特殊字符
        const cleanAccount = accountNumber.replace(/\s+/g, '');
        
        // 长度检查
        const [minLength, maxLength] = this.config.payee.accountNumberLength;
        if (cleanAccount.length < minLength || cleanAccount.length > maxLength) {
            return {
                type: ValidationResult.ERROR,
                field: 'accountNumber',
                message: `账号长度应为${minLength}-${maxLength}位`,
                code: 'ACCOUNT_LENGTH_INVALID'
            };
        }
        
        // 数字格式检查
        if (!/^\d+$/.test(cleanAccount)) {
            return {
                type: ValidationResult.ERROR,
                field: 'accountNumber',
                message: '账号只能包含数字',
                code: 'ACCOUNT_FORMAT_INVALID'
            };
        }
        
        // Luhn算法校验（简化版）
        if (!this._validateLuhn(cleanAccount)) {
            return {
                type: ValidationResult.WARNING,
                field: 'accountNumber',
                message: '账号格式可能有误，请仔细核对',
                code: 'ACCOUNT_CHECKSUM_WARNING'
            };
        }
        
        return {
            type: ValidationResult.VALID,
            field: 'accountNumber',
            message: '账号格式正确',
            code: 'ACCOUNT_VALID'
        };
    }
    
    /**
     * 验证账户姓名
     * @private
     */
    _validateAccountName(accountName) {
        if (!accountName || typeof accountName !== 'string') {
            return {
                type: ValidationResult.WARNING,
                field: 'accountName',
                message: '建议填写收款人姓名以确保转账准确',
                code: 'NAME_RECOMMENDED'
            };
        }
        
        const trimmedName = accountName.trim();
        
        // 长度检查
        if (trimmedName.length < this.config.payee.nameMinLength) {
            return {
                type: ValidationResult.ERROR,
                field: 'accountName',
                message: `姓名长度不能少于${this.config.payee.nameMinLength}个字符`,
                code: 'NAME_TOO_SHORT'
            };
        }
        
        if (trimmedName.length > this.config.payee.nameMaxLength) {
            return {
                type: ValidationResult.ERROR,
                field: 'accountName',
                message: `姓名长度不能超过${this.config.payee.nameMaxLength}个字符`,
                code: 'NAME_TOO_LONG'
            };
        }
        
        // 黑名单关键词检查
        const lowerName = trimmedName.toLowerCase();
        for (const keyword of this.config.payee.blacklistKeywords) {
            if (lowerName.includes(keyword)) {
                return {
                    type: ValidationResult.WARNING,
                    field: 'accountName',
                    message: '姓名包含可疑内容，请确认是否正确',
                    code: 'NAME_SUSPICIOUS'
                };
            }
        }
        
        return {
            type: ValidationResult.VALID,
            field: 'accountName',
            message: '姓名格式正确',
            code: 'NAME_VALID'
        };
    }
    
    /**
     * 检查黑名单
     * @private
     */
    _checkBlacklist(accountNumber) {
        if (this.blacklistedAccounts.has(accountNumber)) {
            return {
                type: ValidationResult.ERROR,
                field: 'accountNumber',
                message: '该账户存在风险，无法转账',
                code: 'ACCOUNT_BLACKLISTED'
            };
        }
        
        return {
            type: ValidationResult.VALID,
            field: 'accountNumber',
            message: '账户安全检查通过',
            code: 'SECURITY_CHECK_PASSED'
        };
    }
    
    /**
     * 检查常用收款人
     * @private
     */
    _checkFrequentPayee(accountNumber) {
        const payeeInfo = this.frequentPayees.get(accountNumber);
        
        if (payeeInfo) {
            return {
                type: ValidationResult.SUGGESTION,
                field: 'accountNumber',
                message: `这是您的常用收款人：${payeeInfo.name}`,
                code: 'FREQUENT_PAYEE_DETECTED',
                data: payeeInfo
            };
        }
        
        return null;
    }
    
    /**
     * 验证账号姓名匹配（模拟）
     * @private
     */
    _validateAccountNameMatch(accountNumber, accountName) {
        // 模拟银行接口验证
        const knownAccounts = {
            '6222021234567890123': '李女士',
            '6222021234567890456': '王先生',
            '6222021234567890789': '陈女士'
        };
        
        const expectedName = knownAccounts[accountNumber];
        
        if (expectedName) {
            if (expectedName === accountName) {
                return {
                    type: ValidationResult.VALID,
                    field: 'accountName',
                    message: '账号姓名匹配正确',
                    code: 'NAME_MATCH_CONFIRMED'
                };
            } else {
                return {
                    type: ValidationResult.WARNING,
                    field: 'accountName',
                    message: `系统显示该账号对应姓名为"${expectedName}"，请核实`,
                    code: 'NAME_MISMATCH_WARNING'
                };
            }
        }
        
        // 对于未知账号，返回验证中状态
        return {
            type: ValidationResult.SUGGESTION,
            field: 'accountName',
            message: '正在验证账号姓名匹配...',
            code: 'NAME_VERIFICATION_PENDING'
        };
    }
    
    /**
     * 验证基础金额
     * @private
     */
    _validateBasicAmount(amount) {
        if (amount === null || amount === undefined || amount === '') {
            return {
                type: ValidationResult.ERROR,
                field: 'amount',
                message: '请输入转账金额',
                code: 'AMOUNT_REQUIRED'
            };
        }
        
        const numAmount = parseFloat(amount);
        
        if (isNaN(numAmount)) {
            return {
                type: ValidationResult.ERROR,
                field: 'amount',
                message: '请输入有效的金额',
                code: 'AMOUNT_INVALID_FORMAT'
            };
        }
        
        if (numAmount <= 0) {
            return {
                type: ValidationResult.ERROR,
                field: 'amount',
                message: '转账金额必须大于0',
                code: 'AMOUNT_TOO_SMALL'
            };
        }
        
        if (numAmount < this.config.amount.minAmount) {
            return {
                type: ValidationResult.ERROR,
                field: 'amount',
                message: `最小转账金额为¥${this.config.amount.minAmount}`,
                code: 'AMOUNT_BELOW_MINIMUM'
            };
        }
        
        return {
            type: ValidationResult.VALID,
            field: 'amount',
            message: '金额格式正确',
            code: 'AMOUNT_VALID'
        };
    }
    
    /**
     * 验证金额限制
     * @private
     */
    _validateAmountLimits(amount, context) {
        const numAmount = parseFloat(amount);
        
        // 单笔限额检查
        if (numAmount > this.config.amount.maxSingleAmount) {
            return {
                type: ValidationResult.ERROR,
                field: 'amount',
                message: `单笔转账限额为¥${this.config.amount.maxSingleAmount.toLocaleString()}`,
                code: 'AMOUNT_EXCEEDS_SINGLE_LIMIT'
            };
        }
        
        // 警告阈值检查
        if (numAmount >= this.config.amount.warningThreshold) {
            return {
                type: ValidationResult.WARNING,
                field: 'amount',
                message: `大额转账，请确认金额：¥${numAmount.toLocaleString()}`,
                code: 'LARGE_AMOUNT_WARNING'
            };
        }
        
        return {
            type: ValidationResult.VALID,
            field: 'amount',
            message: '金额在允许范围内',
            code: 'AMOUNT_WITHIN_LIMITS'
        };
    }
    
    /**
     * 检测金额异常
     * @private
     */
    _detectAmountAnomaly(amount, context) {
        const numAmount = parseFloat(amount);
        
        // 整数金额检测（可能是测试或异常）
        if (numAmount >= 10000 && numAmount % 10000 === 0) {
            return {
                type: ValidationResult.SUGGESTION,
                field: 'amount',
                message: '检测到整数金额，请确认是否正确',
                code: 'ROUND_NUMBER_DETECTED'
            };
        }
        
        // 可疑金额模式检测
        if (numAmount >= this.config.amount.suspiciousThreshold) {
            return {
                type: ValidationResult.WARNING,
                field: 'amount',
                message: '大额转账需要额外验证',
                code: 'SUSPICIOUS_AMOUNT_DETECTED'
            };
        }
        
        return null;
    }
    
    /**
     * 分析行为模式
     * @private
     */
    _analyzeBehaviorPattern(amount, context) {
        // 这里可以实现更复杂的行为分析逻辑
        // 目前返回null表示无异常
        return null;
    }
    
    /**
     * 生成收款人建议
     * @private
     */
    _generatePayeeSuggestions(accountNumber, validationResults) {
        const suggestions = [];
        
        // 基于验证结果生成建议
        for (const result of validationResults) {
            if (result.type === ValidationResult.WARNING || result.type === ValidationResult.ERROR) {
                switch (result.code) {
                    case 'ACCOUNT_LENGTH_INVALID':
                        suggestions.push('请检查账号位数是否正确');
                        break;
                    case 'NAME_MISMATCH_WARNING':
                        suggestions.push('建议联系收款人确认姓名');
                        break;
                }
            }
        }
        
        return suggestions;
    }
    
    /**
     * 生成金额建议
     * @private
     */
    _generateAmountSuggestions(amount, validationResults) {
        const suggestions = [];
        
        for (const result of validationResults) {
            if (result.type === ValidationResult.WARNING) {
                switch (result.code) {
                    case 'LARGE_AMOUNT_WARNING':
                        suggestions.push('大额转账建议分批进行');
                        break;
                    case 'ROUND_NUMBER_DETECTED':
                        suggestions.push('确认金额是否包含小数部分');
                        break;
                }
            }
        }
        
        return suggestions;
    }
    
    /**
     * 获取基于历史的金额建议
     * @private
     */
    _getHistoryBasedAmountSuggestions() {
        // 模拟历史交易数据
        const historyAmounts = [1500, 2000, 3000, 5000];
        
        return historyAmounts.map(amount => ({
            amount: amount,
            displayText: `¥${amount.toLocaleString()}`,
            reason: '历史常用',
            confidence: 85
        }));
    }
    
    /**
     * 计算推荐分数
     * @private
     */
    _calculateRecommendationScore(payeeInfo) {
        const frequencyScore = Math.min(payeeInfo.frequency * 2, 30);
        const recencyScore = Math.max(30 - (Date.now() - payeeInfo.lastUsed) / (******** * 7), 0);
        
        return frequencyScore + recencyScore;
    }
    
    /**
     * 掩码账号显示
     * @private
     */
    _maskAccountNumber(accountNumber) {
        if (accountNumber.length <= 8) {
            return accountNumber;
        }
        
        const start = accountNumber.substring(0, 4);
        const end = accountNumber.substring(accountNumber.length - 4);
        const middle = '*'.repeat(accountNumber.length - 8);
        
        return start + middle + end;
    }
    
    /**
     * Luhn算法校验（简化版）
     * @private
     */
    _validateLuhn(accountNumber) {
        // 简化的Luhn算法实现
        // 在实际应用中应该使用完整的算法
        return accountNumber.length >= 16;
    }
    
    /**
     * 实时账号验证
     * @private
     */
    _getRealTimeAccountValidation(value) {
        if (!value) {
            return { isValid: true, message: '', suggestions: [] };
        }
        
        const validation = this._validateAccountNumber(value);
        const suggestions = [];
        
        // 添加自动完成建议
        if (value.length >= 4) {
            const recommendations = this.getFrequentPayeeRecommendations(value, 3);
            suggestions.push(...recommendations.map(r => r.displayText));
        }
        
        return {
            isValid: validation.type !== ValidationResult.ERROR,
            message: validation.message,
            suggestions: suggestions
        };
    }
    
    /**
     * 实时金额验证
     * @private
     */
    _getRealTimeAmountValidation(value, context) {
        if (!value) {
            return { isValid: true, message: '', suggestions: [] };
        }
        
        const validation = this._validateBasicAmount(value);
        const suggestions = [];
        
        // 添加金额建议
        if (value.length >= 2) {
            const amountSuggestions = this.getSmartAmountSuggestions(context);
            suggestions.push(...amountSuggestions.slice(0, 3).map(s => s.displayText));
        }
        
        return {
            isValid: validation.type !== ValidationResult.ERROR,
            message: validation.message,
            suggestions: suggestions
        };
    }
    
    /**
     * 实时姓名验证
     * @private
     */
    _getRealTimeNameValidation(value) {
        if (!value) {
            return { isValid: true, message: '', suggestions: [] };
        }
        
        const validation = this._validateAccountName(value);
        
        return {
            isValid: validation.type !== ValidationResult.ERROR,
            message: validation.message,
            suggestions: []
        };
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听演示重置事件
        eventBus.on(DemoEvents.DEMO_RESET, () => {
            this.transactionHistory = [];
        });
    }
}

// 创建全局智能验证系统实例
export const smartValidationSystem = new SmartValidationSystem();

// 导出智能验证系统类
export default SmartValidationSystem;

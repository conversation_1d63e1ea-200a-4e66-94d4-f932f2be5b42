/**
 * AI思维链模块 - 负责AI思维过程的可视化展示
 * 
 * 这个模块专门处理AI思维链的生成、展示和管理，
 * 为演示系统提供透明的AI决策过程展示
 */

import { eventBus, DemoEvents } from '../../core/event-bus.js';
import { stateManager } from '../../core/state-manager.js';

/**
 * AI思维链类
 * 管理AI思维过程的可视化展示
 */
class ThinkingChain {
    constructor() {
        // 当前思维步骤
        this.currentSteps = [];
        
        // 思维链状态
        this.isActive = false;
        
        // 配置
        this.config = {
            stepDelay: 800,
            showConfidence: true,
            showIcons: true
        };
        
        // 思维步骤图标映射
        this.stepIcons = {
            'detection': '🔍',
            'analysis': '📊', 
            'recommendation': '💡',
            'generation': '⚡',
            'preparation': '📋',
            'query': '🔎',
            'optimization': '⚙️',
            'scan': '🔍',
            'calculation': '🧮',
            'evaluation': '⚖️',
            'decision': '🎯',
            'validation': '✅',
            'processing': '⚡',
            'learning': '🧠',
            'reasoning': '🤔'
        };
        
        this._setupEventListeners();
    }
    
    /**
     * 开始思维链展示
     * @param {Array} steps - 思维步骤数组
     * @param {Object} options - 选项
     */
    async start(steps, options = {}) {
        if (this.isActive) {
            console.warn('[ThinkingChain] Already active, stopping current chain');
            this.stop();
        }
        
        this.currentSteps = steps;
        this.isActive = true;
        
        // 更新配置
        Object.assign(this.config, options);
        
        // 更新状态
        stateManager.updateState({
            'ai.isThinking': true,
            'ai.thinkingSteps': steps,
            'ai.currentThinkingStep': 0
        });
        
        // 触发思维开始事件
        eventBus.emit(DemoEvents.AI_THINKING_START, {
            steps: this.currentSteps,
            totalSteps: this.currentSteps.length
        });
        
        console.log('[ThinkingChain] Started with', steps.length, 'steps');
        
        // 开始逐步展示
        await this._displaySteps();
    }
    
    /**
     * 停止思维链展示
     */
    stop() {
        if (!this.isActive) {
            return;
        }
        
        this.isActive = false;
        
        // 更新状态
        stateManager.updateState({
            'ai.isThinking': false,
            'ai.currentThinkingStep': 0
        });
        
        // 触发思维结束事件
        eventBus.emit(DemoEvents.AI_THINKING_END, {
            steps: this.currentSteps,
            completed: false
        });
        
        console.log('[ThinkingChain] Stopped');
    }
    
    /**
     * 生成银信通业务思维步骤
     * @param {string} businessType - 业务类型 ('signup' | 'modify' | 'cancel')
     * @param {Object} context - 业务上下文
     * @returns {Array} 思维步骤数组
     */
    generateYinxintongSteps(businessType, context = {}) {
        const baseSteps = [
            { type: 'detection', text: '检测到银信通相关需求', confidence: 98 },
            { type: 'analysis', text: '分析用户历史行为模式', confidence: 95 }
        ];
        
        switch (businessType) {
            case 'signup':
                return [
                    ...baseSteps,
                    { type: 'query', text: '查询用户账户信息', confidence: 100 },
                    { type: 'evaluation', text: '评估用户风险等级', confidence: 92 },
                    { type: 'calculation', text: '计算最优通知阈值', confidence: 89 },
                    { type: 'generation', text: '生成个性化方案建议', confidence: 96 },
                    { type: 'preparation', text: '准备签约流程步骤', confidence: 100 }
                ];
                
            case 'modify':
                return [
                    ...baseSteps,
                    { type: 'query', text: '查询当前银信通设置', confidence: 100 },
                    { type: 'analysis', text: '分析使用习惯变化', confidence: 87 },
                    { type: 'optimization', text: '优化通知策略', confidence: 91 },
                    { type: 'generation', text: '生成修改建议', confidence: 94 }
                ];
                
            case 'cancel':
                return [
                    ...baseSteps,
                    { type: 'evaluation', text: '评估解约原因', confidence: 85 },
                    { type: 'analysis', text: '分析替代方案可能性', confidence: 78 },
                    { type: 'generation', text: '生成挽留建议', confidence: 82 },
                    { type: 'preparation', text: '准备解约确认流程', confidence: 100 }
                ];
                
            default:
                return baseSteps;
        }
    }
    
    /**
     * 生成转账业务思维步骤
     * @param {string} transferType - 转账类型
     * @param {Object} context - 业务上下文
     * @returns {Array} 思维步骤数组
     */
    generateTransferSteps(transferType, context = {}) {
        const baseSteps = [
            { type: 'detection', text: '识别转账需求', confidence: 98 },
            { type: 'validation', text: '验证账户权限', confidence: 100 }
        ];

        switch (transferType) {
            case 'same_bank':
                return [
                    ...baseSteps,
                    { type: 'query', text: '查询付款账户余额', confidence: 100 },
                    { type: 'analysis', text: '分析转账风险等级', confidence: 94 },
                    { type: 'validation', text: '验证收款人账户有效性', confidence: 96 },
                    { type: 'calculation', text: '计算手续费（本行免费）', confidence: 100 },
                    { type: 'evaluation', text: '评估交易合规性', confidence: 92 },
                    { type: 'preparation', text: '准备转账执行流程', confidence: 100 }
                ];

            case 'other_bank':
                return [
                    ...baseSteps,
                    { type: 'query', text: '查询跨行转账通道', confidence: 98 },
                    { type: 'analysis', text: '分析跨行转账风险', confidence: 90 },
                    { type: 'calculation', text: '计算跨行手续费', confidence: 100 },
                    { type: 'optimization', text: '选择最优转账路径', confidence: 88 },
                    { type: 'preparation', text: '准备跨行转账流程', confidence: 95 }
                ];

            case 'international':
                return [
                    ...baseSteps,
                    { type: 'analysis', text: '分析国际汇款合规要求', confidence: 85 },
                    { type: 'validation', text: '验证外汇管制政策', confidence: 92 },
                    { type: 'calculation', text: '计算汇率和手续费', confidence: 95 },
                    { type: 'evaluation', text: '评估反洗钱风险', confidence: 88 },
                    { type: 'preparation', text: '准备国际汇款流程', confidence: 90 }
                ];

            default:
                return [
                    ...baseSteps,
                    { type: 'analysis', text: '分析转账风险', confidence: 94 },
                    { type: 'calculation', text: '计算手续费用', confidence: 100 },
                    { type: 'recommendation', text: '推荐最优转账方式', confidence: 91 },
                    { type: 'preparation', text: '准备转账流程', confidence: 100 }
                ];
        }
    }

    /**
     * 生成密码重置思维步骤
     * @param {string} resetType - 重置类型
     * @param {Object} context - 业务上下文
     * @returns {Array} 思维步骤数组
     */
    generatePasswordResetSteps(resetType, context = {}) {
        return [
            { type: 'detection', text: '检测到密码重置需求', confidence: 98 },
            { type: 'analysis', text: '分析用户身份信息', confidence: 95 },
            { type: 'evaluation', text: '评估安全风险等级', confidence: 92 },
            { type: 'query', text: '查询历史安全记录', confidence: 88 },
            { type: 'validation', text: '验证多重身份认证', confidence: 96 },
            { type: 'generation', text: '生成安全验证流程', confidence: 94 },
            { type: 'preparation', text: '准备密码重置步骤', confidence: 100 }
        ];
    }

    /**
     * 生成账户查询思维步骤
     * @param {string} queryType - 查询类型
     * @param {Object} context - 业务上下文
     * @returns {Array} 思维步骤数组
     */
    generateAccountQuerySteps(queryType, context = {}) {
        const baseSteps = [
            { type: 'detection', text: '识别账户查询需求', confidence: 98 },
            { type: 'validation', text: '验证查询权限', confidence: 100 }
        ];

        switch (queryType) {
            case 'balance':
                return [
                    ...baseSteps,
                    { type: 'query', text: '查询实时账户余额', confidence: 100 },
                    { type: 'analysis', text: '分析余额变动趋势', confidence: 85 },
                    { type: 'generation', text: '生成余额报告', confidence: 95 }
                ];

            case 'transaction_history':
                return [
                    ...baseSteps,
                    { type: 'query', text: '查询交易历史记录', confidence: 100 },
                    { type: 'analysis', text: '分析交易模式', confidence: 88 },
                    { type: 'processing', text: '处理交易分类', confidence: 92 },
                    { type: 'generation', text: '生成交易报表', confidence: 95 }
                ];

            default:
                return [
                    ...baseSteps,
                    { type: 'query', text: '查询账户信息', confidence: 100 },
                    { type: 'generation', text: '生成查询结果', confidence: 95 }
                ];
        }
    }
    
    /**
     * 生成通用业务思维步骤
     * @param {string} intent - 用户意图
     * @param {Object} context - 上下文
     * @returns {Array} 思维步骤数组
     */
    generateGenericSteps(intent, context = {}) {
        return [
            { type: 'detection', text: '理解用户意图', confidence: 95 },
            { type: 'analysis', text: '分析业务需求', confidence: 90 },
            { type: 'query', text: '查询相关信息', confidence: 98 },
            { type: 'reasoning', text: '推理最佳方案', confidence: 87 },
            { type: 'generation', text: '生成回复内容', confidence: 93 }
        ];
    }
    
    /**
     * 设置思维链配置
     * @param {Object} config - 配置对象
     */
    setConfig(config) {
        Object.assign(this.config, config);
        console.log('[ThinkingChain] Config updated:', this.config);
    }
    
    /**
     * 获取当前思维状态
     * @returns {Object} 思维状态
     */
    getState() {
        return {
            isActive: this.isActive,
            currentSteps: this.currentSteps,
            currentStepIndex: stateManager.getState('ai.currentThinkingStep') || 0,
            totalSteps: this.currentSteps.length
        };
    }
    
    /**
     * 逐步展示思维过程
     * @private
     */
    async _displaySteps() {
        for (let i = 0; i < this.currentSteps.length; i++) {
            if (!this.isActive) {
                break; // 如果被停止，退出循环
            }
            
            const step = this.currentSteps[i];
            
            // 更新当前步骤
            stateManager.updateState('ai.currentThinkingStep', i);
            
            // 触发步骤事件
            eventBus.emit(DemoEvents.AI_THINKING_STEP, {
                step: this._formatStep(step),
                index: i,
                total: this.currentSteps.length
            });
            
            // 等待延迟
            await this._delay(this.config.stepDelay);
        }
        
        // 如果完整完成，触发结束事件
        if (this.isActive) {
            this.isActive = false;
            
            stateManager.updateState('ai.isThinking', false);
            
            eventBus.emit(DemoEvents.AI_THINKING_END, {
                steps: this.currentSteps,
                completed: true
            });
            
            console.log('[ThinkingChain] Completed successfully');
        }
    }
    
    /**
     * 格式化思维步骤
     * @private
     */
    _formatStep(step) {
        const formatted = {
            text: step.text || step,
            confidence: step.confidence || null,
            type: step.type || 'generic'
        };
        
        // 添加图标
        if (this.config.showIcons && step.type) {
            formatted.icon = this.stepIcons[step.type] || '🤔';
        }
        
        return formatted;
    }
    
    /**
     * 延迟函数
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听演示速度变化
        eventBus.on(DemoEvents.ANIMATION_SPEED_CHANGED, (event) => {
            const speed = event.data.speed;
            const baseDelay = 800;
            
            switch (speed) {
                case 'slow':
                    this.config.stepDelay = baseDelay * 1.5;
                    break;
                case 'fast':
                    this.config.stepDelay = baseDelay * 0.5;
                    break;
                default:
                    this.config.stepDelay = baseDelay;
            }
            
            console.log('[ThinkingChain] Step delay updated to:', this.config.stepDelay);
        });
        
        // 监听演示停止事件
        eventBus.on(DemoEvents.DEMO_STOPPED, () => {
            this.stop();
        });
        
        // 监听演示重置事件
        eventBus.on(DemoEvents.DEMO_RESET, () => {
            this.stop();
            this.currentSteps = [];
        });
    }
}

// 创建全局思维链实例
export const thinkingChain = new ThinkingChain();

// 导出思维链类
export default ThinkingChain;

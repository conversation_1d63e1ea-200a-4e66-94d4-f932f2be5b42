<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>核心银行业务模块测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: #0F172A; 
            color: #F1F5F9; 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .test-section {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        .test-pass { background: #065F46; color: #D1FAE5; }
        .test-fail { background: #7F1D1D; color: #FEE2E2; }
        .test-pending { background: #92400E; color: #FEF3C7; }
    </style>
</head>
<body class="p-6">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">核心银行业务模块测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">测试控制</h2>
            <div class="flex space-x-3 mb-4">
                <button id="test-yinxintong" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white">
                    测试银信通模块
                </button>
                <button id="test-transfer" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white">
                    测试转账模块
                </button>
                <button id="test-password" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded text-white">
                    测试密码重置模块
                </button>
                <button id="test-all" class="px-4 py-2 bg-orange-600 hover:bg-orange-700 rounded text-white">
                    测试所有模块
                </button>
            </div>
            <div id="test-summary" class="text-sm text-gray-400">
                点击按钮开始测试...
            </div>
        </div>
        
        <!-- 银信通模块测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">银信通签约模块</h3>
            <div id="yinxintong-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 转账模块测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">本行转账模块</h3>
            <div id="transfer-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 密码重置模块测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">密码重置模块</h3>
            <div id="password-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <!-- 集成测试 -->
        <div class="test-section">
            <h3 class="text-md font-semibold mb-3">集成测试</h3>
            <div id="integration-tests" class="space-y-1">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
    </div>
    
    <script type="module">
        // 测试结果显示
        function showTestResult(containerId, testName, status, message = '') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${status}`;
            
            const statusIcon = {
                'pass': '✅',
                'fail': '❌',
                'pending': '⏳'
            }[status];
            
            resultDiv.textContent = `${statusIcon} ${testName}${message ? ` - ${message}` : ''}`;
            container.appendChild(resultDiv);
        }
        
        // 清空测试结果
        function clearTestResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        // 测试银信通模块
        async function testYinxintongModule() {
            clearTestResults('yinxintong-tests');
            
            try {
                showTestResult('yinxintong-tests', '模块加载', 'pending');
                const { yinxintongSignupFlow } = await import('./modules/banking-services/yinxintong/signup-flow.js');
                showTestResult('yinxintong-tests', '模块加载', 'pass');
                
                showTestResult('yinxintong-tests', '流程步骤验证', 'pending');
                if (yinxintongSignupFlow.steps && yinxintongSignupFlow.steps.length > 0) {
                    showTestResult('yinxintong-tests', '流程步骤验证', 'pass', `${yinxintongSignupFlow.steps.length}个步骤`);
                } else {
                    showTestResult('yinxintong-tests', '流程步骤验证', 'fail', '步骤未定义');
                }
                
                showTestResult('yinxintong-tests', '启动流程测试', 'pending');
                await yinxintongSignupFlow.start();
                showTestResult('yinxintong-tests', '启动流程测试', 'pass');
                
                showTestResult('yinxintong-tests', '停止流程测试', 'pending');
                yinxintongSignupFlow.stop();
                showTestResult('yinxintong-tests', '停止流程测试', 'pass');
                
            } catch (error) {
                showTestResult('yinxintong-tests', '模块测试', 'fail', error.message);
            }
        }
        
        // 测试转账模块
        async function testTransferModule() {
            clearTestResults('transfer-tests');
            
            try {
                showTestResult('transfer-tests', '模块加载', 'pending');
                const { sameBankTransferFlow } = await import('./modules/banking-services/transfer/same-bank-transfer.js');
                showTestResult('transfer-tests', '模块加载', 'pass');
                
                showTestResult('transfer-tests', '流程步骤验证', 'pending');
                if (sameBankTransferFlow.steps && sameBankTransferFlow.steps.length > 0) {
                    showTestResult('transfer-tests', '流程步骤验证', 'pass', `${sameBankTransferFlow.steps.length}个步骤`);
                } else {
                    showTestResult('transfer-tests', '流程步骤验证', 'fail', '步骤未定义');
                }
                
                showTestResult('transfer-tests', '启动流程测试', 'pending');
                await sameBankTransferFlow.start();
                showTestResult('transfer-tests', '启动流程测试', 'pass');
                
                showTestResult('transfer-tests', '用户输入测试', 'pending');
                sameBankTransferFlow.handleUserInput('submit-transfer-info', {
                    toAccount: '**********',
                    amount: 1000,
                    purpose: '测试'
                });
                showTestResult('transfer-tests', '用户输入测试', 'pass');
                
                showTestResult('transfer-tests', '停止流程测试', 'pending');
                sameBankTransferFlow.stop();
                showTestResult('transfer-tests', '停止流程测试', 'pass');
                
            } catch (error) {
                showTestResult('transfer-tests', '模块测试', 'fail', error.message);
            }
        }
        
        // 测试密码重置模块
        async function testPasswordModule() {
            clearTestResults('password-tests');
            
            try {
                showTestResult('password-tests', '模块加载', 'pending');
                const { passwordResetFlow } = await import('./modules/banking-services/security/password-reset.js');
                showTestResult('password-tests', '模块加载', 'pass');
                
                showTestResult('password-tests', '流程步骤验证', 'pending');
                if (passwordResetFlow.steps && passwordResetFlow.steps.length > 0) {
                    showTestResult('password-tests', '流程步骤验证', 'pass', `${passwordResetFlow.steps.length}个步骤`);
                } else {
                    showTestResult('password-tests', '流程步骤验证', 'fail', '步骤未定义');
                }
                
                showTestResult('password-tests', '启动流程测试', 'pending');
                await passwordResetFlow.start();
                showTestResult('password-tests', '启动流程测试', 'pass');
                
                showTestResult('password-tests', '用户输入测试', 'pending');
                passwordResetFlow.handleUserInput('identity-verified');
                showTestResult('password-tests', '用户输入测试', 'pass');
                
                showTestResult('password-tests', '停止流程测试', 'pending');
                passwordResetFlow.stop();
                showTestResult('password-tests', '停止流程测试', 'pass');
                
            } catch (error) {
                showTestResult('password-tests', '模块测试', 'fail', error.message);
            }
        }
        
        // 集成测试
        async function testIntegration() {
            clearTestResults('integration-tests');
            
            try {
                showTestResult('integration-tests', 'AI思维链集成', 'pending');
                const { thinkingChain } = await import('./modules/ai-engine/thinking-chain.js');
                
                // 测试银信通思维链
                const yinxintongSteps = thinkingChain.generateYinxintongSteps('signup');
                if (yinxintongSteps.length > 0) {
                    showTestResult('integration-tests', '银信通思维链生成', 'pass', `${yinxintongSteps.length}步`);
                } else {
                    showTestResult('integration-tests', '银信通思维链生成', 'fail');
                }
                
                // 测试转账思维链
                const transferSteps = thinkingChain.generateTransferSteps('same_bank');
                if (transferSteps.length > 0) {
                    showTestResult('integration-tests', '转账思维链生成', 'pass', `${transferSteps.length}步`);
                } else {
                    showTestResult('integration-tests', '转账思维链生成', 'fail');
                }
                
                // 测试密码重置思维链
                const passwordSteps = thinkingChain.generatePasswordResetSteps('reset');
                if (passwordSteps.length > 0) {
                    showTestResult('integration-tests', '密码重置思维链生成', 'pass', `${passwordSteps.length}步`);
                } else {
                    showTestResult('integration-tests', '密码重置思维链生成', 'fail');
                }
                
                showTestResult('integration-tests', 'AI思维链集成', 'pass');
                
                showTestResult('integration-tests', '事件系统集成', 'pending');
                const { eventBus } = await import('./core/event-bus.js');
                
                let eventReceived = false;
                const unsubscribe = eventBus.on('test:integration', () => {
                    eventReceived = true;
                });
                
                eventBus.emit('test:integration');
                
                if (eventReceived) {
                    showTestResult('integration-tests', '事件系统集成', 'pass');
                } else {
                    showTestResult('integration-tests', '事件系统集成', 'fail');
                }
                
                unsubscribe();
                
            } catch (error) {
                showTestResult('integration-tests', '集成测试', 'fail', error.message);
            }
        }
        
        // 测试所有模块
        async function testAllModules() {
            document.getElementById('test-summary').textContent = '正在运行所有测试...';
            
            await testYinxintongModule();
            await testTransferModule();
            await testPasswordModule();
            await testIntegration();
            
            // 统计结果
            const allResults = document.querySelectorAll('.test-result');
            const passed = document.querySelectorAll('.test-pass').length;
            const failed = document.querySelectorAll('.test-fail').length;
            const total = allResults.length;
            
            document.getElementById('test-summary').innerHTML = `
                测试完成！总计: ${total} | 
                <span class="text-green-400">通过: ${passed}</span> | 
                <span class="text-red-400">失败: ${failed}</span> | 
                成功率: ${Math.round((passed / total) * 100)}%
            `;
        }
        
        // 设置事件监听
        document.getElementById('test-yinxintong').addEventListener('click', testYinxintongModule);
        document.getElementById('test-transfer').addEventListener('click', testTransferModule);
        document.getElementById('test-password').addEventListener('click', testPasswordModule);
        document.getElementById('test-all').addEventListener('click', testAllModules);
        
        console.log('核心银行业务模块测试页面已加载');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能防错与验证机制测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: #0F172A; 
            color: #F1F5F9; 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .test-section {
            background: #1E293B;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .validation-demo {
            background: #374151;
            border: 1px solid #4B5563;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .validation-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        .result-valid { background: #065F46; color: #D1FAE5; }
        .result-warning { background: #92400E; color: #FEF3C7; }
        .result-error { background: #7F1D1D; color: #FEE2E2; }
        .result-suggestion { background: #1E40AF; color: #DBEAFE; }
    </style>
</head>
<body class="p-6">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">智能防错与验证机制测试</h1>
        
        <!-- 实时验证演示 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">实时验证演示</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 收款人验证 -->
                <div class="validation-demo">
                    <h3 class="text-md font-medium mb-3">收款人信息验证</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">收款账号</label>
                            <input type="text" id="demo-account" 
                                   class="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                                   placeholder="请输入收款人账号">
                            <div id="account-result" class="mt-2"></div>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">收款人姓名</label>
                            <input type="text" id="demo-name" 
                                   class="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                                   placeholder="请输入收款人姓名">
                            <div id="name-result" class="mt-2"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 金额验证 -->
                <div class="validation-demo">
                    <h3 class="text-md font-medium mb-3">转账金额验证</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">转账金额</label>
                            <input type="number" id="demo-amount" 
                                   class="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                                   placeholder="请输入转账金额">
                            <div id="amount-result" class="mt-2"></div>
                        </div>
                        <div>
                            <button id="validate-all" class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white">
                                完整验证
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 智能推荐演示 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">智能推荐演示</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 常用收款人推荐 -->
                <div class="validation-demo">
                    <h3 class="text-md font-medium mb-3">常用收款人推荐</h3>
                    <div class="space-y-3">
                        <button id="show-frequent-payees" class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white">
                            显示常用收款人
                        </button>
                        <div id="frequent-payees-result" class="space-y-2"></div>
                    </div>
                </div>
                
                <!-- 智能金额建议 -->
                <div class="validation-demo">
                    <h3 class="text-md font-medium mb-3">智能金额建议</h3>
                    <div class="space-y-3">
                        <button id="show-amount-suggestions" class="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded text-white">
                            显示金额建议
                        </button>
                        <div id="amount-suggestions-result" class="grid grid-cols-4 gap-2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 验证规则测试 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">验证规则测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="test-account-rules" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white">
                    测试账号规则
                </button>
                <button id="test-amount-rules" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white">
                    测试金额规则
                </button>
                <button id="test-security-rules" class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-white">
                    测试安全规则
                </button>
            </div>
            <div id="rules-test-result" class="mt-4 space-y-2"></div>
        </div>
        
        <!-- 测试用例 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">预设测试用例</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                <button onclick="testCase('valid')" class="px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm text-white">
                    正常案例
                </button>
                <button onclick="testCase('invalid-account')" class="px-3 py-2 bg-red-600 hover:bg-red-700 rounded text-sm text-white">
                    无效账号
                </button>
                <button onclick="testCase('large-amount')" class="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded text-sm text-white">
                    大额转账
                </button>
                <button onclick="testCase('frequent-payee')" class="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm text-white">
                    常用收款人
                </button>
                <button onclick="testCase('blacklist')" class="px-3 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm text-white">
                    黑名单账户
                </button>
                <button onclick="testCase('suspicious-name')" class="px-3 py-2 bg-orange-600 hover:bg-orange-700 rounded text-sm text-white">
                    可疑姓名
                </button>
                <button onclick="testCase('round-amount')" class="px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded text-sm text-white">
                    整数金额
                </button>
                <button onclick="testCase('exceed-limit')" class="px-3 py-2 bg-pink-600 hover:bg-pink-700 rounded text-sm text-white">
                    超限金额
                </button>
            </div>
        </div>
        
        <!-- 验证统计 -->
        <div class="test-section">
            <h2 class="text-lg font-semibold mb-4">验证统计</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="bg-gray-800 rounded-lg p-4">
                    <div class="text-2xl font-bold text-green-400" id="valid-count">0</div>
                    <div class="text-sm text-gray-400">通过验证</div>
                </div>
                <div class="bg-gray-800 rounded-lg p-4">
                    <div class="text-2xl font-bold text-yellow-400" id="warning-count">0</div>
                    <div class="text-sm text-gray-400">警告提示</div>
                </div>
                <div class="bg-gray-800 rounded-lg p-4">
                    <div class="text-2xl font-bold text-red-400" id="error-count">0</div>
                    <div class="text-sm text-gray-400">验证失败</div>
                </div>
                <div class="bg-gray-800 rounded-lg p-4">
                    <div class="text-2xl font-bold text-blue-400" id="suggestion-count">0</div>
                    <div class="text-sm text-gray-400">智能建议</div>
                </div>
            </div>
        </div>
    </div>
    
    <script type="module">
        // 导入智能验证模块
        import { smartValidationSystem, ValidationResult } from './modules/validation/smart-validation.js';
        
        // 验证统计
        let stats = {
            valid: 0,
            warning: 0,
            error: 0,
            suggestion: 0
        };
        
        // 显示验证结果
        function showValidationResult(containerId, results) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            container.innerHTML = '';
            
            results.forEach(result => {
                const div = document.createElement('div');
                div.className = `validation-result result-${result.type}`;
                
                const icon = {
                    [ValidationResult.VALID]: '✅',
                    [ValidationResult.WARNING]: '⚠️',
                    [ValidationResult.ERROR]: '❌',
                    [ValidationResult.SUGGESTION]: '💡'
                }[result.type];
                
                div.textContent = `${icon} ${result.message}`;
                container.appendChild(div);
                
                // 更新统计
                stats[result.type]++;
                updateStats();
            });
        }
        
        // 更新统计显示
        function updateStats() {
            document.getElementById('valid-count').textContent = stats.valid;
            document.getElementById('warning-count').textContent = stats.warning;
            document.getElementById('error-count').textContent = stats.error;
            document.getElementById('suggestion-count').textContent = stats.suggestion;
        }
        
        // 实时验证
        function setupRealTimeValidation() {
            const accountInput = document.getElementById('demo-account');
            const nameInput = document.getElementById('demo-name');
            const amountInput = document.getElementById('demo-amount');
            
            accountInput.addEventListener('input', (e) => {
                const validation = smartValidationSystem.getRealTimeValidation('accountNumber', e.target.value);
                showValidationMessage('account-result', validation);
            });
            
            nameInput.addEventListener('input', (e) => {
                const validation = smartValidationSystem.getRealTimeValidation('accountName', e.target.value);
                showValidationMessage('name-result', validation);
            });
            
            amountInput.addEventListener('input', (e) => {
                const validation = smartValidationSystem.getRealTimeValidation('amount', e.target.value);
                showValidationMessage('amount-result', validation);
            });
        }
        
        // 显示验证消息
        function showValidationMessage(containerId, validation) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            if (validation.message) {
                const className = validation.isValid ? 'text-green-400' : 'text-red-400';
                container.innerHTML = `<div class="text-xs ${className}">${validation.message}</div>`;
            } else {
                container.innerHTML = '';
            }
            
            // 显示建议
            if (validation.suggestions && validation.suggestions.length > 0) {
                const suggestionsDiv = document.createElement('div');
                suggestionsDiv.className = 'mt-2 space-y-1';
                suggestionsDiv.innerHTML = validation.suggestions.map(s => 
                    `<div class="text-xs text-blue-400 cursor-pointer hover:text-blue-300" onclick="applySuggestion('${containerId}', '${s}')">${s}</div>`
                ).join('');
                container.appendChild(suggestionsDiv);
            }
        }
        
        // 应用建议
        window.applySuggestion = function(containerId, suggestion) {
            if (containerId === 'account-result') {
                document.getElementById('demo-account').value = suggestion.split(' ')[0];
            } else if (containerId === 'amount-result') {
                const amount = suggestion.match(/¥([\d,]+)/);
                if (amount) {
                    document.getElementById('demo-amount').value = amount[1].replace(/,/g, '');
                }
            }
        };
        
        // 完整验证
        document.getElementById('validate-all').addEventListener('click', () => {
            const account = document.getElementById('demo-account').value;
            const name = document.getElementById('demo-name').value;
            const amount = document.getElementById('demo-amount').value;
            
            if (account) {
                const payeeValidation = smartValidationSystem.validatePayeeInfo(account, name);
                showValidationResult('account-result', payeeValidation.results);
            }
            
            if (amount) {
                const amountValidation = smartValidationSystem.validateTransferAmount(amount);
                showValidationResult('amount-result', amountValidation.results);
            }
        });
        
        // 显示常用收款人
        document.getElementById('show-frequent-payees').addEventListener('click', () => {
            const recommendations = smartValidationSystem.getFrequentPayeeRecommendations();
            const container = document.getElementById('frequent-payees-result');
            
            container.innerHTML = recommendations.map(rec => `
                <div class="p-2 bg-gray-700 rounded cursor-pointer hover:bg-gray-600"
                     onclick="selectPayee('${rec.accountNumber}', '${rec.name}')">
                    <div class="font-medium">${rec.name}</div>
                    <div class="text-xs text-gray-400">${rec.displayText}</div>
                    <div class="text-xs text-blue-400">使用${rec.frequency}次 | 置信度${rec.confidence}%</div>
                </div>
            `).join('');
        });
        
        // 选择收款人
        window.selectPayee = function(account, name) {
            document.getElementById('demo-account').value = account;
            document.getElementById('demo-name').value = name;
        };
        
        // 显示金额建议
        document.getElementById('show-amount-suggestions').addEventListener('click', () => {
            const suggestions = smartValidationSystem.getSmartAmountSuggestions();
            const container = document.getElementById('amount-suggestions-result');
            
            container.innerHTML = suggestions.map(sug => `
                <button class="p-2 bg-gray-700 hover:bg-gray-600 rounded text-xs"
                        onclick="selectAmount(${sug.amount})">
                    ${sug.displayText}
                    <div class="text-xs text-gray-400">${sug.reason}</div>
                </button>
            `).join('');
        });
        
        // 选择金额
        window.selectAmount = function(amount) {
            document.getElementById('demo-amount').value = amount;
        };
        
        // 测试用例
        const testCases = {
            'valid': { account: '6222021234567890123', name: '李女士', amount: 1000 },
            'invalid-account': { account: '123', name: '张三', amount: 500 },
            'large-amount': { account: '6222021234567890456', name: '王先生', amount: 80000 },
            'frequent-payee': { account: '6222021234567890123', name: '李女士', amount: 2000 },
            'blacklist': { account: '6222021234567890000', name: '黑名单', amount: 1000 },
            'suspicious-name': { account: '6222021234567890789', name: '测试账户', amount: 500 },
            'round-amount': { account: '6222021234567890456', name: '王先生', amount: 50000 },
            'exceed-limit': { account: '6222021234567890123', name: '李女士', amount: 600000 }
        };
        
        window.testCase = function(caseType) {
            const testCase = testCases[caseType];
            if (!testCase) return;
            
            document.getElementById('demo-account').value = testCase.account;
            document.getElementById('demo-name').value = testCase.name;
            document.getElementById('demo-amount').value = testCase.amount;
            
            // 触发验证
            setTimeout(() => {
                document.getElementById('validate-all').click();
            }, 100);
        };
        
        // 规则测试
        document.getElementById('test-account-rules').addEventListener('click', () => {
            const testAccounts = ['123', '6222021234567890123', '6222021234567890000', 'abc123'];
            const container = document.getElementById('rules-test-result');
            
            container.innerHTML = '<h4 class="font-medium mb-2">账号规则测试结果：</h4>';
            
            testAccounts.forEach(account => {
                const validation = smartValidationSystem.validatePayeeInfo(account);
                const div = document.createElement('div');
                div.className = 'text-sm p-2 bg-gray-800 rounded';
                div.innerHTML = `
                    <strong>${account}:</strong> 
                    ${validation.isValid ? '<span class="text-green-400">通过</span>' : '<span class="text-red-400">失败</span>'}
                    - ${validation.results[0]?.message || '无消息'}
                `;
                container.appendChild(div);
            });
        });
        
        document.getElementById('test-amount-rules').addEventListener('click', () => {
            const testAmounts = [0, 100, 50000, 600000, -100];
            const container = document.getElementById('rules-test-result');
            
            container.innerHTML = '<h4 class="font-medium mb-2">金额规则测试结果：</h4>';
            
            testAmounts.forEach(amount => {
                const validation = smartValidationSystem.validateTransferAmount(amount);
                const div = document.createElement('div');
                div.className = 'text-sm p-2 bg-gray-800 rounded';
                div.innerHTML = `
                    <strong>¥${amount}:</strong> 
                    ${validation.isValid ? '<span class="text-green-400">通过</span>' : '<span class="text-red-400">失败</span>'}
                    - ${validation.results[0]?.message || '无消息'}
                `;
                container.appendChild(div);
            });
        });
        
        // 初始化
        setupRealTimeValidation();
        updateStats();
        
        console.log('智能防错与验证机制测试页面已加载');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试 - 银信通AI助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .test-result {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-pass {
            background-color: #10b981;
            color: white;
        }
        .test-fail {
            background-color: #ef4444;
            color: white;
        }
        .test-info {
            background-color: #3b82f6;
            color: white;
        }
    </style>
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary); color: var(--text-primary);">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold mb-6" style="color: var(--text-primary);">银信通AI助手 - 功能测试</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 主题切换测试 -->
            <div class="rounded-lg p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                <h2 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">主题切换测试</h2>
                <div id="theme-test-results"></div>
                <button onclick="testThemeToggle()" class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                    测试主题切换
                </button>
                <button onclick="testThemeToggleButton()" class="mt-4 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg">
                    测试切换按钮
                </button>
            </div>

            <!-- JavaScript功能测试 -->
            <div class="rounded-lg p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                <h2 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">JavaScript功能测试</h2>
                <div id="js-test-results"></div>
                <button onclick="testJavaScriptFunctions()" class="mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
                    测试JS功能
                </button>
            </div>

            <!-- CSS样式测试 -->
            <div class="rounded-lg p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                <h2 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">CSS样式测试</h2>
                <div id="css-test-results"></div>
                <button onclick="testCSSStyles()" class="mt-4 px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg">
                    测试CSS样式
                </button>
            </div>

            <!-- 交互元素测试 -->
            <div class="rounded-lg p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                <h2 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">交互元素测试</h2>
                <div id="interaction-test-results"></div>
                <button onclick="testInteractionElements()" class="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg">
                    测试交互元素
                </button>
            </div>
        </div>

        <!-- 综合测试 -->
        <div class="mt-8 rounded-lg p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
            <h2 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">综合功能测试</h2>
            <div id="comprehensive-test-results"></div>
            <button onclick="runComprehensiveTest()" class="mt-4 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-medium">
                运行完整测试
            </button>
        </div>
    </div>

    <script>
        // 测试结果显示函数
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result test-${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearTestResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 主题切换测试
        function testThemeToggle() {
            clearTestResults('theme-test-results');
            addTestResult('theme-test-results', '开始主题切换测试...', 'info');

            try {
                const html = document.documentElement;
                const currentTheme = html.getAttribute('data-theme') || 'dark';
                addTestResult('theme-test-results', `当前主题: ${currentTheme}`, 'info');

                // 测试主题切换
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                html.setAttribute('data-theme', newTheme);
                addTestResult('theme-test-results', `切换到: ${newTheme}`, 'pass');

                // 验证CSS变量
                const styles = getComputedStyle(document.documentElement);
                const bgPrimary = styles.getPropertyValue('--bg-primary').trim();
                const textPrimary = styles.getPropertyValue('--text-primary').trim();
                
                if (bgPrimary && textPrimary) {
                    addTestResult('theme-test-results', `CSS变量正常: bg=${bgPrimary}, text=${textPrimary}`, 'pass');
                } else {
                    addTestResult('theme-test-results', 'CSS变量未定义', 'fail');
                }

                // 切换回原主题
                setTimeout(() => {
                    html.setAttribute('data-theme', currentTheme);
                    addTestResult('theme-test-results', `恢复原主题: ${currentTheme}`, 'pass');
                }, 1000);

            } catch (error) {
                addTestResult('theme-test-results', `主题切换错误: ${error.message}`, 'fail');
            }
        }

        // 测试主题切换按钮
        function testThemeToggleButton() {
            clearTestResults('theme-test-results');
            addTestResult('theme-test-results', '测试主题切换按钮...', 'info');

            try {
                const themeButton = document.getElementById('theme-toggle');
                if (themeButton) {
                    addTestResult('theme-test-results', '主题切换按钮存在', 'pass');
                    
                    // 模拟点击
                    themeButton.click();
                    addTestResult('theme-test-results', '按钮点击成功', 'pass');
                } else {
                    addTestResult('theme-test-results', '主题切换按钮不存在', 'fail');
                }
            } catch (error) {
                addTestResult('theme-test-results', `按钮测试错误: ${error.message}`, 'fail');
            }
        }

        // JavaScript功能测试
        function testJavaScriptFunctions() {
            clearTestResults('js-test-results');
            addTestResult('js-test-results', '测试JavaScript功能...', 'info');

            try {
                // 测试全局aiAgent对象
                if (typeof aiAgent !== 'undefined') {
                    addTestResult('js-test-results', 'aiAgent对象存在', 'pass');
                    
                    // 测试关键方法
                    const methods = ['addAIMessage', 'addUserMessage', 'toggleTheme', 'sendMessage'];
                    methods.forEach(method => {
                        if (typeof aiAgent[method] === 'function') {
                            addTestResult('js-test-results', `方法 ${method} 存在`, 'pass');
                        } else {
                            addTestResult('js-test-results', `方法 ${method} 不存在`, 'fail');
                        }
                    });
                } else {
                    addTestResult('js-test-results', 'aiAgent对象不存在', 'fail');
                }

                // 测试事件监听器
                const userInput = document.getElementById('user-input');
                if (userInput) {
                    addTestResult('js-test-results', '用户输入框存在', 'pass');
                } else {
                    addTestResult('js-test-results', '用户输入框不存在', 'fail');
                }

            } catch (error) {
                addTestResult('js-test-results', `JavaScript测试错误: ${error.message}`, 'fail');
            }
        }

        // CSS样式测试
        function testCSSStyles() {
            clearTestResults('css-test-results');
            addTestResult('css-test-results', '测试CSS样式...', 'info');

            try {
                // 测试CSS变量定义
                const styles = getComputedStyle(document.documentElement);
                const cssVars = [
                    '--bg-primary', '--bg-secondary', '--text-primary', '--text-secondary',
                    '--border-primary', '--accent-blue'
                ];

                cssVars.forEach(varName => {
                    const value = styles.getPropertyValue(varName).trim();
                    if (value) {
                        addTestResult('css-test-results', `${varName}: ${value}`, 'pass');
                    } else {
                        addTestResult('css-test-results', `${varName} 未定义`, 'fail');
                    }
                });

                // 测试明亮模式样式
                const html = document.documentElement;
                const originalTheme = html.getAttribute('data-theme');
                html.setAttribute('data-theme', 'light');
                
                setTimeout(() => {
                    const lightBg = styles.getPropertyValue('--bg-primary').trim();
                    if (lightBg) {
                        addTestResult('css-test-results', '明亮模式样式正常', 'pass');
                    } else {
                        addTestResult('css-test-results', '明亮模式样式异常', 'fail');
                    }
                    html.setAttribute('data-theme', originalTheme);
                }, 100);

            } catch (error) {
                addTestResult('css-test-results', `CSS测试错误: ${error.message}`, 'fail');
            }
        }

        // 交互元素测试
        function testInteractionElements() {
            clearTestResults('interaction-test-results');
            addTestResult('interaction-test-results', '测试交互元素...', 'info');

            try {
                // 测试关键元素存在性
                const elements = [
                    'theme-toggle', 'user-input', 'chat-messages', 
                    'personalized-greeting', 'ai-thinking'
                ];

                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        addTestResult('interaction-test-results', `元素 #${id} 存在`, 'pass');
                    } else {
                        addTestResult('interaction-test-results', `元素 #${id} 不存在`, 'fail');
                    }
                });

                // 测试按钮点击
                const buttons = document.querySelectorAll('button[onclick]');
                addTestResult('interaction-test-results', `找到 ${buttons.length} 个onclick按钮`, 'info');

            } catch (error) {
                addTestResult('interaction-test-results', `交互测试错误: ${error.message}`, 'fail');
            }
        }

        // 综合测试
        function runComprehensiveTest() {
            clearTestResults('comprehensive-test-results');
            addTestResult('comprehensive-test-results', '开始综合功能测试...', 'info');

            // 依次运行所有测试
            setTimeout(() => testThemeToggle(), 100);
            setTimeout(() => testJavaScriptFunctions(), 500);
            setTimeout(() => testCSSStyles(), 1000);
            setTimeout(() => testInteractionElements(), 1500);

            setTimeout(() => {
                addTestResult('comprehensive-test-results', '所有测试完成', 'pass');
            }, 2000);
        }

        // 页面加载时自动运行基础测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                addTestResult('comprehensive-test-results', '页面加载完成，可以开始测试', 'info');
            }, 1000);
        });
    </script>
</body>
</html>

// ===== 动画控制器 =====

class AnimationController {
    constructor() {
        this.observers = new Map();
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupClickEffects();
    }

    // 设置交叉观察器，用于滚动动画
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target);
                }
            });
        }, observerOptions);

        // 观察所有需要动画的元素
        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });

        this.observers.set('intersection', observer);
    }

    // 设置滚动动画
    setupScrollAnimations() {
        let ticking = false;

        const updateScrollAnimations = () => {
            const scrollY = window.scrollY;
            const windowHeight = window.innerHeight;

            // 视差效果
            document.querySelectorAll('[data-parallax]').forEach(el => {
                const speed = parseFloat(el.dataset.parallax) || 0.5;
                const yPos = -(scrollY * speed);
                el.style.transform = `translateY(${yPos}px)`;
            });

            // 渐显效果
            document.querySelectorAll('[data-fade-in]').forEach(el => {
                const rect = el.getBoundingClientRect();
                const isVisible = rect.top < windowHeight && rect.bottom > 0;
                
                if (isVisible && !el.classList.contains('fade-in-active')) {
                    el.classList.add('fade-in-active', 'animate-fade-in-up');
                }
            });

            ticking = false;
        };

        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestScrollUpdate, { passive: true });
    }

    // 设置悬停效果
    setupHoverEffects() {
        // 卡片悬停效果
        document.querySelectorAll('.card, .solution-card').forEach(card => {
            card.classList.add('interactive-hover');

            card.addEventListener('mouseenter', () => {
                card.classList.add('hover-lift');
                this.addMagneticEffect(card);
            });

            card.addEventListener('mouseleave', () => {
                card.classList.remove('hover-lift');
                this.removeMagneticEffect(card);
            });
        });

        // 按钮悬停效果
        document.querySelectorAll('button').forEach(button => {
            button.classList.add('interactive-hover');

            button.addEventListener('mouseenter', () => {
                if (!button.disabled) {
                    button.classList.add('hover-scale');
                    this.addButtonGlow(button);
                }
            });

            button.addEventListener('mouseleave', () => {
                button.classList.remove('hover-scale');
                this.removeButtonGlow(button);
            });

            // 按钮点击波纹效果
            button.addEventListener('click', (e) => {
                this.createAdvancedRipple(e, button);
            });
        });

        // 图标悬停效果
        document.querySelectorAll('.icon-hover, svg').forEach(icon => {
            icon.addEventListener('mouseenter', () => {
                icon.classList.add('animate-bounce');
                this.addIconPulse(icon);
            });

            icon.addEventListener('mouseleave', () => {
                icon.classList.remove('animate-bounce');
                this.removeIconPulse(icon);
            });
        });

        // 输入框聚焦效果
        document.querySelectorAll('input, textarea').forEach(input => {
            input.addEventListener('focus', () => {
                this.addInputFocusEffect(input);
            });

            input.addEventListener('blur', () => {
                this.removeInputFocusEffect(input);
            });
        });

        // 链接悬停效果
        document.querySelectorAll('a, .clickable').forEach(link => {
            link.addEventListener('mouseenter', () => {
                this.addLinkHoverEffect(link);
            });

            link.addEventListener('mouseleave', () => {
                this.removeLinkHoverEffect(link);
            });
        });
    }

    addMagneticEffect(element) {
        element.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

        const handleMouseMove = (e) => {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const deltaX = (e.clientX - centerX) * 0.1;
            const deltaY = (e.clientY - centerY) * 0.1;

            element.style.transform = `translate(${deltaX}px, ${deltaY}px) translateY(-2px)`;
        };

        element.addEventListener('mousemove', handleMouseMove);
        element._magneticHandler = handleMouseMove;
    }

    removeMagneticEffect(element) {
        if (element._magneticHandler) {
            element.removeEventListener('mousemove', element._magneticHandler);
            delete element._magneticHandler;
        }
        element.style.transform = '';
    }

    addButtonGlow(button) {
        button.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.4)';
        button.style.transition = 'all 0.3s ease';
    }

    removeButtonGlow(button) {
        button.style.boxShadow = '';
    }

    addIconPulse(icon) {
        icon.classList.add('status-pulse');
    }

    removeIconPulse(icon) {
        icon.classList.remove('status-pulse');
    }

    addInputFocusEffect(input) {
        input.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
        input.style.borderColor = '#3b82f6';

        // 添加聚焦动画
        input.classList.add('animate-elastic-in');
        setTimeout(() => {
            input.classList.remove('animate-elastic-in');
        }, 600);
    }

    removeInputFocusEffect(input) {
        input.style.boxShadow = '';
        input.style.borderColor = '';
    }

    addLinkHoverEffect(link) {
        link.style.transform = 'translateY(-1px)';
        link.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
    }

    removeLinkHoverEffect(link) {
        link.style.transform = '';
        link.style.textShadow = '';
    }

    createAdvancedRipple(event, element) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.1) 70%, transparent 100%);
            border-radius: 50%;
            transform: scale(0);
            animation: advancedRipple 0.6s ease-out;
            pointer-events: none;
            z-index: 1;
        `;

        // 确保按钮有相对定位
        if (getComputedStyle(element).position === 'static') {
            element.style.position = 'relative';
        }
        element.style.overflow = 'hidden';

        element.appendChild(ripple);

        // 动画结束后移除元素
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // 设置点击效果
    setupClickEffects() {
        document.addEventListener('click', (e) => {
            // 波纹效果
            if (e.target.matches('button, .clickable')) {
                this.createRippleEffect(e);
            }

            // 按钮点击反馈
            if (e.target.matches('button')) {
                this.addClickFeedback(e.target);
            }
        });
    }

    // 创建波纹效果
    createRippleEffect(event) {
        const button = event.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;

        // 确保按钮有相对定位
        if (getComputedStyle(button).position === 'static') {
            button.style.position = 'relative';
        }
        button.style.overflow = 'hidden';

        button.appendChild(ripple);

        // 动画结束后移除元素
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // 添加点击反馈
    addClickFeedback(element) {
        element.classList.add('animate-pulse');
        setTimeout(() => {
            element.classList.remove('animate-pulse');
        }, 300);
    }

    // 动画元素
    animateElement(element) {
        const animationType = element.dataset.animate;
        
        switch (animationType) {
            case 'fade-in':
                element.classList.add('animate-fade-in');
                break;
            case 'slide-up':
                element.classList.add('animate-slide-in-up');
                break;
            case 'slide-left':
                element.classList.add('animate-slide-in-left');
                break;
            case 'slide-right':
                element.classList.add('animate-slide-in-right');
                break;
            case 'scale':
                element.classList.add('animate-fade-in-scale');
                break;
            default:
                element.classList.add('animate-fade-in-up');
        }

        // 添加延迟
        const delay = element.dataset.delay;
        if (delay) {
            element.style.animationDelay = delay + 'ms';
        }
    }

    // 数字计数动画
    animateNumber(element, start, end, duration = 1000) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.round(current).toLocaleString();
        }, 16);
    }

    // 进度条动画
    animateProgressBar(element, targetWidth, duration = 1000) {
        element.style.width = '0%';
        element.style.transition = `width ${duration}ms ease-out`;
        
        setTimeout(() => {
            element.style.width = targetWidth + '%';
        }, 50);
    }

    // 打字机效果
    typeWriter(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        const timer = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    // 淡入淡出切换
    fadeToggle(element, duration = 300) {
        if (element.style.opacity === '0' || !element.style.opacity) {
            this.fadeIn(element, duration);
        } else {
            this.fadeOut(element, duration);
        }
    }

    fadeIn(element, duration = 300) {
        element.style.opacity = '0';
        element.style.display = 'block';
        element.style.transition = `opacity ${duration}ms ease`;
        
        setTimeout(() => {
            element.style.opacity = '1';
        }, 10);
    }

    fadeOut(element, duration = 300) {
        element.style.transition = `opacity ${duration}ms ease`;
        element.style.opacity = '0';
        
        setTimeout(() => {
            element.style.display = 'none';
        }, duration);
    }

    // 滑动显示/隐藏
    slideDown(element, duration = 300) {
        element.style.height = '0px';
        element.style.overflow = 'hidden';
        element.style.transition = `height ${duration}ms ease`;
        element.style.display = 'block';
        
        const targetHeight = element.scrollHeight + 'px';
        setTimeout(() => {
            element.style.height = targetHeight;
        }, 10);
        
        setTimeout(() => {
            element.style.height = 'auto';
            element.style.overflow = 'visible';
        }, duration);
    }

    slideUp(element, duration = 300) {
        element.style.height = element.offsetHeight + 'px';
        element.style.overflow = 'hidden';
        element.style.transition = `height ${duration}ms ease`;
        
        setTimeout(() => {
            element.style.height = '0px';
        }, 10);
        
        setTimeout(() => {
            element.style.display = 'none';
        }, duration);
    }

    // 震动效果
    shake(element, intensity = 5, duration = 500) {
        const originalTransform = element.style.transform;
        const shakeAnimation = element.animate([
            { transform: `translateX(0px)` },
            { transform: `translateX(${intensity}px)` },
            { transform: `translateX(-${intensity}px)` },
            { transform: `translateX(${intensity}px)` },
            { transform: `translateX(0px)` }
        ], {
            duration: duration,
            iterations: 3
        });

        shakeAnimation.onfinish = () => {
            element.style.transform = originalTransform;
        };
    }

    // 脉冲高亮
    pulse(element, color = '#3b82f6', duration = 1000) {
        const originalBoxShadow = element.style.boxShadow;
        
        element.animate([
            { boxShadow: `0 0 0 0 ${color}40` },
            { boxShadow: `0 0 0 10px ${color}00` }
        ], {
            duration: duration,
            iterations: 1
        }).onfinish = () => {
            element.style.boxShadow = originalBoxShadow;
        };
    }

    // 弹性缩放
    elasticScale(element, scale = 1.1, duration = 300) {
        element.animate([
            { transform: 'scale(1)' },
            { transform: `scale(${scale})` },
            { transform: 'scale(0.95)' },
            { transform: 'scale(1)' }
        ], {
            duration: duration,
            easing: 'ease-out'
        });
    }

    // 清理观察器
    destroy() {
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();
    }
}

// 添加高级动画的CSS
const advancedAnimationStyle = document.createElement('style');
advancedAnimationStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }

    @keyframes advancedRipple {
        0% {
            transform: scale(0);
            opacity: 1;
        }
        50% {
            transform: scale(1);
            opacity: 0.8;
        }
        100% {
            transform: scale(2);
            opacity: 0;
        }
    }

    @keyframes morphButton {
        0% {
            border-radius: 8px;
        }
        50% {
            border-radius: 20px;
            transform: scale(1.05);
        }
        100% {
            border-radius: 8px;
            transform: scale(1);
        }
    }

    .morph-button:hover {
        animation: morphButton 0.6s ease-in-out;
    }

    @keyframes floatingIcon {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-5px);
        }
    }

    .floating-icon {
        animation: floatingIcon 2s ease-in-out infinite;
    }

    @keyframes glowPulse {
        0%, 100% {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
        }
        50% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4);
        }
    }

    .glow-pulse {
        animation: glowPulse 2s ease-in-out infinite;
    }

    @keyframes slideInFromBottom {
        0% {
            transform: translateY(100%);
            opacity: 0;
        }
        100% {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .slide-in-bottom {
        animation: slideInFromBottom 0.5s ease-out;
    }

    @keyframes typewriter {
        from {
            width: 0;
        }
        to {
            width: 100%;
        }
    }

    .typewriter {
        overflow: hidden;
        white-space: nowrap;
        animation: typewriter 2s steps(40, end);
    }
`;
document.head.appendChild(advancedAnimationStyle);

// 初始化动画控制器
const animationController = new AnimationController();

// 页面加载完成后的入场动画
document.addEventListener('DOMContentLoaded', () => {
    // 导航栏动画
    const nav = document.querySelector('nav');
    if (nav) {
        nav.classList.add('animate-slide-in-down');
    }

    // 主要内容区域动画
    const main = document.querySelector('main');
    if (main) {
        main.classList.add('animate-fade-in-up');
        main.style.animationDelay = '200ms';
    }

    // 底部输入区域动画
    const footer = document.querySelector('main + div');
    if (footer) {
        footer.classList.add('animate-slide-in-up');
        footer.style.animationDelay = '400ms';
    }

    // 统计数字动画
    setTimeout(() => {
        const statsNumbers = document.querySelectorAll('.text-lg.font-bold');
        statsNumbers.forEach(num => {
            const text = num.textContent;
            if (text.includes('万')) {
                animationController.animateNumber(num, 0, 23000, 1500);
                num.textContent = '2.3万';
            } else if (text.includes('%')) {
                animationController.animateNumber(num, 0, 98.5, 1500);
                num.textContent = '98.5%';
            }
        });
    }, 1000);
});

// 导出动画控制器供其他脚本使用
window.animationController = animationController;
